# SNPITC Email System - Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the SNPITC Email System in a production environment using Docker and Docker Compose.

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended)
- **CPU**: Minimum 2 cores, 4+ cores recommended
- **RAM**: Minimum 4GB, 8GB+ recommended
- **Storage**: Minimum 50GB SSD, 100GB+ recommended
- **Network**: Static IP address with proper DNS configuration

### Software Requirements

- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL
- Curl

### Domain and DNS Setup

1. **Domain Configuration**:
   - Point your domain to the server IP address
   - Configure subdomain if needed (e.g., mail.yourdomain.com)

2. **DNS Records**:
   ```
   A     mail.yourdomain.com    -> YOUR_SERVER_IP
   MX    yourdomain.com         -> mail.yourdomain.com (priority 10)
   TXT   yourdomain.com         -> "v=spf1 ip4:YOUR_SERVER_IP ~all"
   TXT   _dmarc.yourdomain.com  -> "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
   ```

## Quick Deployment

### 1. Clone Repository

```bash
git clone https://github.com/your-org/snpitc-remake.git
cd snpitc-remake
```

### 2. Run Deployment Script

```bash
chmod +x scripts/deploy.sh
sudo DOMAIN=your-domain.com ./scripts/deploy.sh
```

### 3. Access Application

- **Web Interface**: https://your-domain.com
- **Grafana Dashboard**: http://your-domain.com:3001
- **Prometheus**: http://your-domain.com:9090

## Manual Deployment

### 1. Environment Configuration

Create `.env` file:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
DOMAIN=your-domain.com
POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
NEXTAUTH_SECRET=your_nextauth_secret
GRAFANA_PASSWORD=your_grafana_password
```

### 2. SSL Certificates

Generate self-signed certificates (for testing):

```bash
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"
```

For production, use Let's Encrypt or commercial certificates.

### 3. Build and Deploy

```bash
# Build application
docker-compose build

# Start services
docker-compose up -d

# Run database migrations
docker-compose exec app npx prisma migrate deploy

# Check status
docker-compose ps
```

## Service Architecture

### Core Services

1. **App** (Port 3000): Main Next.js application
2. **Postgres** (Port 5432): Primary database
3. **Redis** (Port 6379): Cache and session storage
4. **Nginx** (Ports 80, 443, 587, 993, 995): Reverse proxy and email protocols

### Monitoring Services

1. **Prometheus** (Port 9090): Metrics collection
2. **Grafana** (Port 3001): Monitoring dashboards
3. **Loki** (Port 3100): Log aggregation
4. **Promtail**: Log collection agent

## Configuration

### Email Protocols

The system supports multiple email protocols:

- **SMTP** (Port 587): Sending emails
- **IMAP** (Port 993): Email access with SSL
- **POP3** (Port 995): Email retrieval with SSL

### Security Features

- SSL/TLS encryption for all connections
- Rate limiting on API endpoints
- Security headers (HSTS, XSS protection, etc.)
- Input validation and sanitization
- CSRF protection

### Performance Optimization

- Redis caching for sessions and frequently accessed data
- Database connection pooling
- Nginx compression and static file serving
- Image optimization and lazy loading

## Monitoring and Logging

### Grafana Dashboards

Access Grafana at `http://your-domain.com:3001`:

- **System Overview**: CPU, memory, disk usage
- **Email Metrics**: SMTP/IMAP/POP3 performance
- **Security Dashboard**: Threat detection and blocking
- **Application Performance**: Response times and errors

### Log Management

Logs are collected by Promtail and stored in Loki:

- Application logs: `/var/log/app/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/`

### Alerts

Configure alerts in Grafana for:

- High CPU/memory usage
- Disk space warnings
- Email delivery failures
- Security threats
- Service downtime

## Backup and Recovery

### Automated Backups

Backups run daily at 2 AM:

```bash
# Manual backup
docker-compose run --rm backup

# Restore from backup
docker-compose exec postgres psql -U snpitc -d snpitc_email < backup_file.sql
```

### Backup Strategy

- **Database**: Daily PostgreSQL dumps
- **Files**: Email attachments and user uploads
- **Configuration**: Environment and config files
- **Retention**: 30 days for daily backups

## Scaling Considerations

### Horizontal Scaling

1. **Load Balancer**: Add multiple app instances behind load balancer
2. **Database**: Consider read replicas for high read workloads
3. **Storage**: Use distributed storage for email attachments
4. **Cache**: Redis cluster for high availability

### Vertical Scaling

1. **CPU**: Increase cores for better concurrent processing
2. **Memory**: More RAM for larger datasets and caching
3. **Storage**: SSD for better I/O performance
4. **Network**: Higher bandwidth for email traffic

## Security Hardening

### Server Security

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 587/tcp
sudo ufw allow 993/tcp
sudo ufw allow 995/tcp
sudo ufw enable

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

### Application Security

1. **Environment Variables**: Never commit secrets to version control
2. **Database**: Use strong passwords and limit connections
3. **SSL**: Use valid certificates from trusted CA
4. **Updates**: Regularly update dependencies and base images

## Troubleshooting

### Common Issues

1. **Service Won't Start**:
   ```bash
   docker-compose logs service_name
   ```

2. **Database Connection Issues**:
   ```bash
   docker-compose exec postgres pg_isready -U snpitc
   ```

3. **SSL Certificate Problems**:
   ```bash
   openssl x509 -in nginx/ssl/cert.pem -text -noout
   ```

4. **Email Delivery Issues**:
   - Check DNS records (MX, SPF, DKIM)
   - Verify firewall rules
   - Check email logs

### Health Checks

```bash
# Application health
curl -f http://localhost:3000/api/health

# Database health
docker-compose exec postgres pg_isready

# Redis health
docker-compose exec redis redis-cli ping
```

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Review monitoring dashboards
   - Check backup integrity
   - Update security patches

2. **Monthly**:
   - Review log files
   - Clean up old backups
   - Performance optimization

3. **Quarterly**:
   - Security audit
   - Dependency updates
   - Capacity planning

### Updates

```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d

# Update dependencies
docker-compose exec app npm update
docker-compose restart app
```

## Support

### Documentation

- [API Documentation](./API.md)
- [User Guide](./USER_GUIDE.md)
- [Development Setup](./README.md)

### Monitoring

- **Grafana**: System and application metrics
- **Logs**: Centralized logging with Loki
- **Alerts**: Email and webhook notifications

### Contact

For technical support and questions:
- Email: <EMAIL>
- Documentation: https://docs.yourdomain.com
- Issues: GitHub Issues
