// Simple test to verify React Hooks order is correct
const fs = require('fs')
const path = require('path')

function testHooksOrder() {
  console.log('🔍 Testing React Hooks Order in StudentEmailPage...\n')

  const filePath = path.join(__dirname, '../src/app/student/email/page.tsx')
  const fileContent = fs.readFileSync(filePath, 'utf8')

  // Check for common hooks order violations
  const issues = []

  // Test 1: Check if useEffect comes before conditional returns
  const useEffectMatches = [...fileContent.matchAll(/useEffect\(/g)]
  const conditionalReturnMatches = [...fileContent.matchAll(/if \(.*\) \{[\s\S]*?return/g)]

  console.log('1. Checking hook placement...')
  console.log(`   Found ${useEffectMatches.length} useEffect calls`)
  console.log(`   Found ${conditionalReturnMatches.length} conditional returns`)

  // Test 2: Check if all hooks are at the top level
  const lines = fileContent.split('\n')
  let inFunction = false
  let hookLines = []
  let conditionalReturnLines = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    if (line.includes('export default function StudentEmailPage()')) {
      inFunction = true
      continue
    }

    if (inFunction) {
      if (line.includes('useEffect(') || line.includes('useState(') || line.includes('useCallback(')) {
        hookLines.push(i + 1)
      }
      
      if (line.includes('if (status ===') && line.includes('return')) {
        conditionalReturnLines.push(i + 1)
      }
    }
  }

  console.log(`   Hook calls found on lines: ${hookLines.join(', ')}`)
  console.log(`   Conditional returns found on lines: ${conditionalReturnLines.join(', ')}`)

  // Test 3: Verify hooks come before conditional logic
  const firstConditionalReturn = Math.min(...conditionalReturnLines)
  const lastHook = Math.max(...hookLines)

  if (lastHook < firstConditionalReturn) {
    console.log('   ✅ All hooks are called before conditional returns')
  } else {
    console.log('   ❌ Some hooks are called after conditional returns')
    issues.push('Hooks called after conditional returns')
  }

  // Test 4: Check for hooks inside conditions
  console.log('\n2. Checking for hooks inside conditions...')
  const hookInsideConditionPattern = /if\s*\([^)]+\)\s*\{[^}]*use[A-Z]/g
  const hooksInConditions = fileContent.match(hookInsideConditionPattern)
  
  if (hooksInConditions) {
    console.log('   ❌ Found hooks inside conditions:')
    hooksInConditions.forEach(match => console.log(`      ${match}`))
    issues.push('Hooks inside conditions')
  } else {
    console.log('   ✅ No hooks found inside conditions')
  }

  // Test 5: Check for hooks in loops
  console.log('\n3. Checking for hooks in loops...')
  const hookInLoopPattern = /(for|while)\s*\([^)]+\)\s*\{[^}]*use[A-Z]/g
  const hooksInLoops = fileContent.match(hookInLoopPattern)
  
  if (hooksInLoops) {
    console.log('   ❌ Found hooks inside loops:')
    hooksInLoops.forEach(match => console.log(`      ${match}`))
    issues.push('Hooks inside loops')
  } else {
    console.log('   ✅ No hooks found inside loops')
  }

  // Test 6: Check for early returns before hooks
  console.log('\n4. Checking for early returns before hooks...')
  const functionStart = fileContent.indexOf('export default function StudentEmailPage()')
  const functionContent = fileContent.substring(functionStart)
  
  const firstHookIndex = functionContent.search(/use[A-Z]/)
  const firstReturnIndex = functionContent.search(/return\s+</)
  
  if (firstHookIndex !== -1 && firstReturnIndex !== -1 && firstReturnIndex < firstHookIndex) {
    console.log('   ❌ Found return statement before hooks')
    issues.push('Return before hooks')
  } else {
    console.log('   ✅ No early returns found before hooks')
  }

  // Summary
  console.log('\n📊 Test Results:')
  if (issues.length === 0) {
    console.log('✅ All React Hooks order tests PASSED!')
    console.log('✅ No Rules of Hooks violations detected')
    console.log('✅ Component should render without hook order errors')
  } else {
    console.log('❌ Found the following issues:')
    issues.forEach(issue => console.log(`   - ${issue}`))
  }

  console.log('\n🔧 Fixed Issues:')
  console.log('✅ Moved all hook calls to the top of the component')
  console.log('✅ Used useCallback to memoize functions')
  console.log('✅ Placed conditional returns after all hooks')
  console.log('✅ Ensured consistent hook call order on every render')

  return issues.length === 0
}

testHooksOrder()
