-- Migration: Fix Payment Gateway Enum and Relations
-- Date: 2024-12-13
-- Description: Critical fixes for payment system

-- Step 1: Add new payment gateway options to enum
ALTER TYPE "PaymentGateway" ADD VALUE IF NOT EXISTS 'RAZORPAY';
ALTER TYPE "PaymentGateway" ADD VALUE IF NOT EXISTS 'STRIPE';

-- Step 2: Rename paidAt to completedAt in payments table
ALTER TABLE "payments" RENAME COLUMN "paid_at" TO "completed_at";

-- Step 3: Remove the conflicting userId column and relation from payments
-- First, drop the foreign key constraint if it exists
ALTER TABLE "payments" DROP CONSTRAINT IF EXISTS "payments_userId_fkey";

-- Drop the userId column
ALTER TABLE "payments" DROP COLUMN IF EXISTS "user_id";

-- Step 4: Create performance indexes for payments table
CREATE INDEX IF NOT EXISTS "payments_catalog_item_id_idx" ON "payments"("catalog_item_id");
CREATE INDEX IF NOT EXISTS "payments_gateway_idx" ON "payments"("gateway");
CREATE INDEX IF NOT EXISTS "payments_student_id_idx" ON "payments"("student_id");
CREATE INDEX IF NOT EXISTS "payments_status_idx" ON "payments"("status");
CREATE INDEX IF NOT EXISTS "payments_created_at_idx" ON "payments"("created_at");

-- Step 5: Create performance indexes for admin_audit_logs table
CREATE INDEX IF NOT EXISTS "admin_audit_logs_user_id_idx" ON "admin_audit_logs"("user_id");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_action_idx" ON "admin_audit_logs"("action");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_resource_idx" ON "admin_audit_logs"("resource");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_created_at_idx" ON "admin_audit_logs"("created_at");

-- Step 6: Add additional useful indexes for email system
CREATE INDEX IF NOT EXISTS "email_accounts_email_idx" ON "email_accounts"("email");
CREATE INDEX IF NOT EXISTS "email_accounts_type_idx" ON "email_accounts"("type");
CREATE INDEX IF NOT EXISTS "email_accounts_is_active_idx" ON "email_accounts"("is_active");

CREATE INDEX IF NOT EXISTS "emails_from_email_idx" ON "emails"("from_email");
CREATE INDEX IF NOT EXISTS "emails_status_idx" ON "emails"("status");
CREATE INDEX IF NOT EXISTS "emails_created_at_idx" ON "emails"("created_at");
CREATE INDEX IF NOT EXISTS "emails_is_spam_idx" ON "emails"("is_spam");

-- Step 7: Add indexes for payment catalog items
CREATE INDEX IF NOT EXISTS "payment_catalog_items_fee_type_idx" ON "payment_catalog_items"("fee_type");
CREATE INDEX IF NOT EXISTS "payment_catalog_items_is_active_idx" ON "payment_catalog_items"("is_active");
CREATE INDEX IF NOT EXISTS "payment_catalog_items_created_at_idx" ON "payment_catalog_items"("created_at");

-- Step 8: Add indexes for system alerts
CREATE INDEX IF NOT EXISTS "system_alerts_type_idx" ON "system_alerts"("type");
CREATE INDEX IF NOT EXISTS "system_alerts_severity_idx" ON "system_alerts"("severity");
CREATE INDEX IF NOT EXISTS "system_alerts_is_acknowledged_idx" ON "system_alerts"("is_acknowledged");
CREATE INDEX IF NOT EXISTS "system_alerts_created_at_idx" ON "system_alerts"("created_at");

-- Verification queries (commented out for production)
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'payments';
-- SELECT indexname FROM pg_indexes WHERE tablename = 'payments';
-- SELECT enumlabel FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'PaymentGateway');
