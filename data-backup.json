{"users": [{"id": "cmc1p577i0000fcy4iafh42k2", "email": "<EMAIL>", "name": "Administrator", "password": "$2b$12$R/04MFL51tZLbWOaUKlB1OM8XI/cSnB1zqwisIoLpby/PfRaltnJi", "role": "ADMIN", "createdAt": "2025-06-18T08:34:57.342Z", "updatedAt": "2025-07-14T11:59:57.042Z"}, {"id": "cmc4cs4i70000fc5sh1sbu4qa", "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>", "password": "$2b$12$b8fbaMAlSBMUVnqvVNLajezON7FUbCLAUyqN8S8T2VkWgdwUJBWpS", "role": "ADMIN", "createdAt": "2025-06-20T05:12:10.448Z", "updatedAt": "2025-06-20T05:12:10.448Z"}], "emailAccounts": [{"id": "cmd1yio8b000dfc3s2ljx4ln8", "email": "<EMAIL>", "password": "$2b$12$gbNNwmbcKMqP38lX8Xa1g.vrQoQYZrNC7tl37/aUVce.7Qs3t3HcS", "type": "STUDENT", "isActive": true, "quota": "**********", "usedQuota": "0", "firstName": null, "lastName": null, "displayName": "<PERSON>", "department": null, "studentId": null, "signature": null, "autoReply": false, "autoReplyMessage": null, "forwardingEmail": null, "lastLogin": "2025-07-14T13:56:19.136Z", "loginAttempts": 0, "lockedUntil": null, "createdAt": "2025-07-13T17:37:04.811Z", "updatedAt": "2025-07-14T13:56:19.138Z", "createdById": "cmc1p577i0000fcy4iafh42k2"}, {"id": "cmd1yio8m000ffc3so8fxyzwb", "email": "<EMAIL>", "password": "$2b$12$KE0wkNs/JPTdX1vf4iLtie9ZdEMembHE7wp7fig0GHfr/mDnLS8Ci", "type": "INSTITUTE", "isActive": true, "quota": "**********", "usedQuota": "0", "firstName": null, "lastName": null, "displayName": "Dr. <PERSON>", "department": null, "studentId": null, "signature": null, "autoReply": false, "autoReplyMessage": null, "forwardingEmail": null, "lastLogin": null, "loginAttempts": 0, "lockedUntil": null, "createdAt": "2025-07-13T17:37:04.823Z", "updatedAt": "2025-07-13T17:37:04.823Z", "createdById": "cmc1p577i0000fcy4iafh42k2"}, {"id": "cmd2nttcj0001fc0ggdn56r3k", "email": "<EMAIL>", "password": "$2b$12$UrkDzpV9IYupGZ4Q6/946ub3oxSu5Ree83lhlGxdu51A6UiKNbuEi", "type": "STUDENT", "isActive": true, "quota": "**********", "usedQuota": "0", "firstName": null, "lastName": null, "displayName": "nav<PERSON><PERSON>", "department": null, "studentId": null, "signature": null, "autoReply": false, "autoReplyMessage": null, "forwardingEmail": null, "lastLogin": null, "loginAttempts": 0, "lockedUntil": null, "createdAt": "2025-07-14T05:25:35.056Z", "updatedAt": "2025-07-14T05:25:35.056Z", "createdById": "cmc1p577i0000fcy4iafh42k2"}, {"id": "cmd2nwucb0008fc0gaeqj0fnq", "email": "<EMAIL>", "password": "$2b$12$iyaJXBN.34xMR8LqzgzCxelsU2vkiAtzoP6jZ/e8RFtif9rC4TS4e", "type": "STUDENT", "isActive": true, "quota": "**********", "usedQuota": "0", "firstName": null, "lastName": null, "displayName": "nav<PERSON><PERSON>", "department": null, "studentId": null, "signature": null, "autoReply": false, "autoReplyMessage": null, "forwardingEmail": null, "lastLogin": "2025-07-14T06:04:46.056Z", "loginAttempts": 0, "lockedUntil": null, "createdAt": "2025-07-14T05:27:56.315Z", "updatedAt": "2025-07-14T06:04:46.059Z", "createdById": "cmc1p577i0000fcy4iafh42k2"}], "paymentItems": [{"id": "cmd1yio8x000hfc3sh1dmmb7n", "name": "<PERSON><PERSON><PERSON>", "description": "Regular semester tuition fee", "feeType": "SEMESTER", "amount": 50000, "currency": "INR", "category": "Academic", "subcategory": null, "isActive": true, "isRecurring": true, "recurringInterval": "SEMESTER", "lateFee": 0, "discountPercentage": 0, "dueDate": null, "applicableFor": "STUDENT", "createdAt": "2025-07-13T17:37:04.834Z", "updatedAt": "2025-07-13T17:37:04.834Z", "deletedAt": null, "createdById": "cmc1p577i0000fcy4iafh42k2"}, {"id": "cmd1yio99000jfc3ses5a8zn0", "name": "Library Fee", "description": "Annual library access fee", "feeType": "LIBRARY", "amount": 2000, "currency": "INR", "category": "Services", "subcategory": null, "isActive": true, "isRecurring": true, "recurringInterval": "YEARLY", "lateFee": 0, "discountPercentage": 0, "dueDate": null, "applicableFor": "STUDENT,INSTITUTE", "createdAt": "2025-07-13T17:37:04.845Z", "updatedAt": "2025-07-13T17:37:04.845Z", "deletedAt": null, "createdById": "cmc1p577i0000fcy4iafh42k2"}], "payments": [{"id": "cmd2lspwi0001fcigjyde1ixy", "transactionId": "TXN1752467324700000", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_0", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_0\",\"method\":\"upi\",\"status\":\"failed\"}", "additionalFee": 14.37991085382584, "totalAmount": 2014.379910853826, "status": "FAILED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-15T18:58:21.971Z", "updatedAt": "2025-07-14T04:28:44.704Z", "completedAt": null}, {"id": "cmd2lspww0003fcigs9da47d9", "transactionId": "TXN1752467324700001", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_1", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_1\",\"method\":\"credit_card\",\"status\":\"processing\"}", "additionalFee": 18.11665453565086, "totalAmount": 2018.116654535651, "status": "PROCESSING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-04T20:26:11.880Z", "updatedAt": "2025-07-14T04:28:44.720Z", "completedAt": null}, {"id": "cmd2lspx60005fcigvspmyf0e", "transactionId": "TXN1752467324700002", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_2", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_2\",\"method\":\"credit_card\",\"status\":\"processing\"}", "additionalFee": 49.05002146058389, "totalAmount": 50049.05002146059, "status": "PROCESSING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-23T18:25:22.144Z", "updatedAt": "2025-07-14T04:28:44.730Z", "completedAt": null}, {"id": "cmd2lspxf0007fcigvvz4pb5e", "transactionId": "TXN1752467324700003", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_3", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_3\",\"method\":\"debit_card\",\"status\":\"failed\"}", "additionalFee": 31.86492941055684, "totalAmount": 50031.86492941056, "status": "FAILED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-19T17:42:19.396Z", "updatedAt": "2025-07-14T04:28:44.739Z", "completedAt": null}, {"id": "cmd2lspxn0009fcigogz9bb6r", "transactionId": "TXN1752467324700004", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_4", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_4\",\"method\":\"debit_card\",\"status\":\"processing\"}", "additionalFee": 32.76850840824568, "totalAmount": 2032.768508408246, "status": "PROCESSING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-05T12:51:59.880Z", "updatedAt": "2025-07-14T04:28:44.747Z", "completedAt": null}, {"id": "cmd2lspxx000bfcigldp5n0aw", "transactionId": "TXN1752467324700005", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "RAZORPAY", "gatewayTxnId": "RAZORPAY_1752467324700_5", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_5\",\"method\":\"wallet\",\"status\":\"refunded\"}", "additionalFee": 13.86349139439923, "totalAmount": 50013.8634913944, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-03T02:44:17.261Z", "updatedAt": "2025-07-14T04:28:44.757Z", "completedAt": null}, {"id": "cmd2lspy5000dfcig2e2zby4s", "transactionId": "TXN1752467324700006", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_6", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_6\",\"method\":\"credit_card\",\"status\":\"cancelled\"}", "additionalFee": 28.52644009132819, "totalAmount": 50028.52644009133, "status": "CANCELLED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-24T10:16:24.075Z", "updatedAt": "2025-07-14T04:28:44.766Z", "completedAt": null}, {"id": "cmd2lspye000ffcigtcg8tadt", "transactionId": "TXN1752467324700007", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_7", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_7\",\"method\":\"net_banking\",\"status\":\"cancelled\"}", "additionalFee": 11.**************, "totalAmount": 50011.***********, "status": "CANCELLED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-14T12:20:00.759Z", "updatedAt": "2025-07-14T04:28:44.774Z", "completedAt": null}, {"id": "cmd2lspym000hfcigunitxhhz", "transactionId": "TXN1752467324700008", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "RAZORPAY", "gatewayTxnId": "RAZORPAY_1752467324700_8", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_8\",\"method\":\"wallet\",\"status\":\"refunded\"}", "additionalFee": 34.**************, "totalAmount": 50034.***********, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-13T17:00:38.348Z", "updatedAt": "2025-07-14T04:28:44.782Z", "completedAt": null}, {"id": "cmd2lspyt000jfcignfy3g3ow", "transactionId": "TXN1752467324700009", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_9", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_9\",\"method\":\"net_banking\",\"status\":\"failed\"}", "additionalFee": 35.***********393, "totalAmount": 50035.***********, "status": "FAILED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-18T01:25:50.910Z", "updatedAt": "2025-07-14T04:28:44.789Z", "completedAt": null}, {"id": "cmd2lspz1000lfcig2117mfua", "transactionId": "TXN1752467324700010", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "STRIPE", "gatewayTxnId": "STRIPE_1752467324700_10", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_10\",\"method\":\"net_banking\",\"status\":\"success\"}", "additionalFee": 30.**************, "totalAmount": 2030.************, "status": "SUCCESS", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-11T09:19:06.803Z", "updatedAt": "2025-07-14T04:28:44.797Z", "completedAt": "2025-07-11T10:07:41.914Z"}, {"id": "cmd2lspz8000nfcig54oun13z", "transactionId": "TXN1752467324700011", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "RAZORPAY", "gatewayTxnId": "RAZORPAY_1752467324700_11", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_11\",\"method\":\"credit_card\",\"status\":\"success\"}", "additionalFee": 37.**************, "totalAmount": 50037.**********, "status": "SUCCESS", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-14T21:51:15.213Z", "updatedAt": "2025-07-14T04:28:44.805Z", "completedAt": "2025-06-14T22:44:44.685Z"}, {"id": "cmd2lspzf000pfcigqj00l4q2", "transactionId": "TXN1752467324700012", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_12", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_12\",\"method\":\"upi\",\"status\":\"failed\"}", "additionalFee": 1.070244756803118, "totalAmount": 2001.070244756803, "status": "FAILED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-05T07:54:39.305Z", "updatedAt": "2025-07-14T04:28:44.811Z", "completedAt": null}, {"id": "cmd2lspzn000rfciger3uxsis", "transactionId": "TXN1752467324700013", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_13", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_13\",\"method\":\"wallet\",\"status\":\"cancelled\"}", "additionalFee": 46.39399348274799, "totalAmount": 50046.39399348275, "status": "CANCELLED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-01T10:27:50.614Z", "updatedAt": "2025-07-14T04:28:44.820Z", "completedAt": null}, {"id": "cmd2lspzv000tfcig0ihonhw2", "transactionId": "TXN1752467324700014", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "PAYU", "gatewayTxnId": "PAYU_1752467324700_14", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_14\",\"method\":\"upi\",\"status\":\"refunded\"}", "additionalFee": 16.45312843579765, "totalAmount": 2016.453128435798, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-23T06:24:55.396Z", "updatedAt": "2025-07-14T04:28:44.827Z", "completedAt": null}, {"id": "cmd2lsq02000vfcigu1rtuvcy", "transactionId": "TXN1752467324700015", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "RAZORPAY", "gatewayTxnId": "RAZORPAY_1752467324700_15", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_15\",\"method\":\"net_banking\",\"status\":\"refunded\"}", "additionalFee": 17.**************, "totalAmount": 50017.***********, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-07T04:36:17.156Z", "updatedAt": "2025-07-14T04:28:44.835Z", "completedAt": null}, {"id": "cmd2lsq0a000xfcigenj08e36", "transactionId": "TXN1752467324700016", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "STRIPE", "gatewayTxnId": "STRIPE_1752467324700_16", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_16\",\"method\":\"credit_card\",\"status\":\"processing\"}", "additionalFee": 12.**************, "totalAmount": 2012.************, "status": "PROCESSING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-06T02:05:27.313Z", "updatedAt": "2025-07-14T04:28:44.842Z", "completedAt": null}, {"id": "cmd2lsq0k000zfcigz58pjlt8", "transactionId": "TXN1752467324700017", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_17", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_17\",\"method\":\"debit_card\",\"status\":\"refunded\"}", "additionalFee": 42.62430337528282, "totalAmount": 2042.624303375283, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-06-21T03:11:35.972Z", "updatedAt": "2025-07-14T04:28:44.852Z", "completedAt": null}, {"id": "cmd2lsq0t0011fcigpy3u4nsz", "transactionId": "TXN1752467324700018", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio8x000hfc3sh1dmmb7n", "amount": 50000, "currency": "INR", "feeType": "SEMESTER", "description": "Payment for Semester Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_18", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_18\",\"method\":\"debit_card\",\"status\":\"refunded\"}", "additionalFee": 36.80088507073883, "totalAmount": 50036.80088507074, "status": "REFUNDED", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-07T01:37:28.993Z", "updatedAt": "2025-07-14T04:28:44.862Z", "completedAt": null}, {"id": "cmd2lsq120013fcigwoe79w7l", "transactionId": "TXN1752467324700019", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Payment for Library Fee", "gateway": "CASHFREE", "gatewayTxnId": "CASHFREE_1752467324700_19", "gatewayResponse": "{\"gateway_payment_id\":\"pay_1752467324700_19\",\"method\":\"net_banking\",\"status\":\"pending\"}", "additionalFee": 8.************419, "totalAmount": 2008.************, "status": "PENDING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-07T21:10:23.771Z", "updatedAt": "2025-07-14T04:28:44.871Z", "completedAt": null}, {"id": "cmd31vh6v0001fch4r8f9ldux", "transactionId": "TXN_1752494327237_508b28314", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Library Fee", "gateway": "PAYU", "gatewayTxnId": null, "gatewayResponse": null, "additionalFee": 0, "totalAmount": 2000, "status": "PENDING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-14T11:58:47.239Z", "updatedAt": "2025-07-14T11:58:47.239Z", "completedAt": null}, {"id": "cmd31vigo0003fch4ybcnevoc", "transactionId": "TXN_1752494328887_f5f4685el", "studentId": "cmd1yio8b000dfc3s2ljx4ln8", "catalogItemId": "cmd1yio99000jfc3ses5a8zn0", "amount": 2000, "currency": "INR", "feeType": "LIBRARY", "description": "Library Fee", "gateway": "PAYU", "gatewayTxnId": null, "gatewayResponse": null, "additionalFee": 0, "totalAmount": 2000, "status": "PENDING", "failureReason": null, "receiptNumber": null, "receiptUrl": null, "createdAt": "2025-07-14T11:58:48.888Z", "updatedAt": "2025-07-14T11:58:48.888Z", "completedAt": null}], "settings": [], "exportedAt": "2025-07-14T14:00:14.180Z"}