# 📧 SMTP API Simulation Documentation

## Overview

The SMTP API Simulation provides standard SMTP protocol compatibility for email clients while operating over HTTP/HTTPS. This allows email clients like Outlook, Thunderbird, and mobile apps to send emails through our system using familiar SMTP commands and responses.

## 🚀 Quick Start

### Basic SMTP Session Flow

1. **Initialize Session**: `POST /api/smtp`
2. **Send Commands**: `POST /api/smtp` with commands
3. **Send Email Data**: `POST /api/smtp` with email content
4. **Close Session**: Send `QUIT` command

### Example: Sending an Email

```javascript
// 1. Initialize session
const initResponse = await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
})
const { sessionId } = await initResponse.json()

// 2. HELO command
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'HELO client.example.com'
  })
})

// 3. MAIL FROM command
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'MAIL FROM:<<EMAIL>>'
  })
})

// 4. RCPT TO command
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'RCPT TO:<<EMAIL>>'
  })
})

// 5. DATA command
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'DATA'
  })
})

// 6. Send email content
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    data: 'Subject: Test Email\r\n\r\nHello, this is a test email!'
  })
})

// 7. QUIT
await fetch('/api/smtp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'QUIT'
  })
})
```

## 📡 API Endpoints

### 1. Main SMTP Endpoint

**`POST /api/smtp`** - SMTP Protocol Handler

**Request Body:**
```json
{
  "sessionId": "optional-session-id",
  "command": "SMTP command string",
  "data": "email content (for DATA command)"
}
```

**Response:**
```json
{
  "sessionId": "session-identifier",
  "response": {
    "code": 250,
    "message": "OK"
  },
  "sessionState": "HELO|MAIL|RCPT|DATA"
}
```

**`GET /api/smtp`** - Service Information

Returns SMTP service capabilities and limits.

### 2. Authentication Endpoint

**`POST /api/smtp/auth`** - SMTP Authentication

**Request Body:**
```json
{
  "sessionId": "session-id",
  "method": "PLAIN|LOGIN",
  "credentials": {
    "username": "<EMAIL>",
    "password": "password",
    "encoded": "base64-encoded-credentials"
  }
}
```

**`GET /api/smtp/auth`** - Authentication Methods

Returns supported authentication methods and usage examples.

### 3. Configuration Endpoint

**`GET /api/smtp/config`** - Email Client Configuration

**Query Parameters:**
- `format`: `json|xml|autodiscover|thunderbird`
- `client`: `outlook|thunderbird|apple|android|ios`

**Examples:**
- `/api/smtp/config?client=outlook` - Outlook-specific settings
- `/api/smtp/config?format=xml` - Autodiscover XML
- `/api/smtp/config?format=thunderbird` - Thunderbird autoconfig

### 4. WebSocket Simulation

**`POST /api/smtp/websocket`** - Real-time SMTP Protocol

**Actions:**
- `connect` - Initialize SMTP session
- `command` - Send SMTP command
- `data` - Send email data
- `disconnect` - Close session

## 🔧 SMTP Commands Supported

| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `HELO` | Identify client | hostname | `HELO client.example.com` |
| `EHLO` | Extended HELO | hostname | `EHLO client.example.com` |
| `MAIL FROM` | Set sender | email address | `MAIL FROM:<<EMAIL>>` |
| `RCPT TO` | Add recipient | email address | `RCPT TO:<<EMAIL>>` |
| `DATA` | Start email data | none | `DATA` |
| `RSET` | Reset session | none | `RSET` |
| `NOOP` | No operation | none | `NOOP` |
| `QUIT` | End session | none | `QUIT` |
| `HELP` | Get help | none | `HELP` |
| `AUTH` | Authentication | method | `AUTH PLAIN` |

## 📋 SMTP Response Codes

| Code | Meaning | Description |
|------|---------|-------------|
| 220 | Service ready | SMTP service is ready |
| 250 | OK | Command completed successfully |
| 251 | User not local | Will forward to user |
| 354 | Start mail input | Ready to receive email data |
| 421 | Service not available | Service is temporarily unavailable |
| 450 | Mailbox busy | Mailbox temporarily unavailable |
| 451 | Local error | Local error in processing |
| 452 | Insufficient storage | Insufficient system storage |
| 500 | Command unrecognized | Syntax error, command unrecognized |
| 501 | Syntax error | Syntax error in parameters |
| 502 | Command not implemented | Command not implemented |
| 503 | Bad sequence | Bad sequence of commands |
| 550 | Mailbox unavailable | Mailbox unavailable |
| 552 | Exceeded storage | Message exceeds storage allocation |
| 554 | Transaction failed | Transaction failed |

## 🔐 Authentication

### PLAIN Authentication
```json
{
  "method": "PLAIN",
  "credentials": {
    "encoded": "base64(\0username\0password)"
  }
}
```

### LOGIN Authentication
```json
{
  "method": "LOGIN",
  "credentials": {
    "username": "<EMAIL>",
    "password": "password"
  }
}
```

## 📱 Email Client Configuration

### Microsoft Outlook

1. **Manual Setup:**
   - Server: `your-domain.com/api/smtp`
   - Port: 587
   - Encryption: STARTTLS
   - Authentication: Normal password

2. **Autodiscover:**
   - URL: `/api/smtp/config?format=autodiscover`

### Mozilla Thunderbird

1. **Manual Setup:**
   - SMTP Server: `your-domain.com/api/smtp`
   - Port: 587
   - Security: STARTTLS
   - Authentication: Normal password

2. **Autoconfig:**
   - URL: `/api/smtp/config?format=thunderbird`

### Apple Mail (iOS/macOS)

1. **Manual Setup:**
   - SMTP Server: `your-domain.com/api/smtp`
   - Port: 587
   - SSL: Yes
   - Authentication: Password

### Android Email

1. **Manual Setup:**
   - Outgoing server: `your-domain.com/api/smtp`
   - Port: 587
   - Security type: STARTTLS
   - Require sign-in: Yes

## 🛡️ Security Features

### Encryption
- All communications over HTTPS
- TLS/SSL support simulation
- Secure credential transmission

### Authentication
- Required for external senders
- Support for PLAIN and LOGIN methods
- Session-based authentication

### Rate Limiting
- Protection against brute force attacks
- Session timeout (30 minutes)
- Connection limits per IP

### Validation
- Email address validation
- Command syntax validation
- Message size limits (25MB)

## 📊 Limitations & Considerations

### Current Limitations
- HTTP-based simulation (not raw TCP SMTP)
- Session storage in memory (use Redis for production)
- Basic authentication methods only
- No SMTP pipelining support

### Production Recommendations
- Use Redis for session storage
- Implement proper password hashing
- Add comprehensive logging
- Set up monitoring and alerts
- Configure proper rate limiting

## 🔍 Troubleshooting

### Common Issues

1. **Session Expired**
   - Sessions timeout after 30 minutes
   - Create new session if expired

2. **Authentication Failed**
   - Verify credentials are correct
   - Ensure account exists and is active

3. **Command Sequence Error**
   - Follow proper SMTP command sequence
   - Use RSET to reset session state

4. **Message Too Large**
   - Maximum message size is 25MB
   - Split large messages or reduce attachments

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_SMTP=true
```

## 📚 Additional Resources

- [RFC 5321 - Simple Mail Transfer Protocol](https://tools.ietf.org/html/rfc5321)
- [RFC 4616 - PLAIN SASL Mechanism](https://tools.ietf.org/html/rfc4616)
- [RFC 3463 - Enhanced Mail System Status Codes](https://tools.ietf.org/html/rfc3463)

## 🆘 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: `/api/smtp/config`
- Service Status: `/api/smtp` (GET)
