const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const allRemainingPages = [
  // Facilities Section (remaining)
  {
    title: 'Computer Lab',
    slug: 'computer-lab',
    description: 'Modern computer laboratory facilities with latest hardware and software for digital literacy training.',
    content: `
      <h2>Computer Lab</h2>
      <h3>Computer Laboratory Facilities</h3>
      <p>S.N. Pvt. Industrial Training Institute has a modern computer laboratory equipped with latest hardware and software.</p>
      
      <h4>Lab Specifications:</h4>
      <ul>
        <li><strong>Area:</strong> 380 Sq. Mt.</li>
        <li><strong>Number of Computers:</strong> 20 systems</li>
        <li><strong>Operating Hours:</strong> 9:00 AM to 5:00 PM</li>
        <li><strong>Internet Connectivity:</strong> High-speed broadband</li>
      </ul>
      
      <h4>Hardware Configuration:</h4>
      <ul>
        <li>Modern desktop computers</li>
        <li>LCD monitors</li>
        <li>Keyboards and mice</li>
        <li>Printers and scanners</li>
        <li>UPS backup systems</li>
        <li>Air conditioning for optimal environment</li>
      </ul>
      
      <h4>Software Available:</h4>
      <ul>
        <li>Windows Operating System</li>
        <li>Microsoft Office Suite</li>
        <li>CAD software for technical drawing</li>
        <li>Programming languages and compilers</li>
        <li>Internet browsers</li>
        <li>Educational software</li>
      </ul>
      
      <h4>Training Programs:</h4>
      <ul>
        <li>Basic computer literacy</li>
        <li>MS Office applications</li>
        <li>Internet and email usage</li>
        <li>Technical drawing using CAD</li>
        <li>Digital documentation</li>
      </ul>
    `,
    metaTitle: 'Computer Lab - S.N. Pvt. ITI',
    metaDesc: 'Modern computer laboratory facilities at S.N. Pvt. ITI with latest hardware, software, and digital literacy training programs.',
    status: 'PUBLISHED'
  },
  {
    title: 'Sports',
    slug: 'sports',
    description: 'Sports and recreational facilities available for physical fitness and overall development of students.',
    content: `
      <h2>Sports</h2>
      <h3>Sports and Recreation Facilities</h3>
      <p>S.N. Pvt. Industrial Training Institute believes in the overall development of students including physical fitness and sports activities.</p>
      
      <h4>Available Sports:</h4>
      <ul>
        <li>Cricket</li>
        <li>Football</li>
        <li>Volleyball</li>
        <li>Badminton</li>
        <li>Table Tennis</li>
        <li>Athletics</li>
      </ul>
      
      <h4>Sports Equipment:</h4>
      <ul>
        <li>Cricket bats, balls, and stumps</li>
        <li>Football and volleyball</li>
        <li>Badminton rackets and shuttlecocks</li>
        <li>Table tennis table and accessories</li>
        <li>Athletic equipment</li>
        <li>Sports shoes and uniforms</li>
      </ul>
      
      <h4>Sports Activities:</h4>
      <ul>
        <li>Daily morning physical exercise</li>
        <li>Inter-class competitions</li>
        <li>Annual sports day</li>
        <li>Participation in district level competitions</li>
        <li>Fitness training programs</li>
      </ul>
      
      <h4>Benefits:</h4>
      <ul>
        <li>Physical fitness and health</li>
        <li>Team spirit and cooperation</li>
        <li>Leadership qualities</li>
        <li>Stress relief and mental well-being</li>
        <li>Discipline and time management</li>
      </ul>
      
      <p>The institute encourages all students to participate in sports activities for their holistic development.</p>
    `,
    metaTitle: 'Sports Facilities - S.N. Pvt. ITI',
    metaDesc: 'Sports and recreational facilities at S.N. Pvt. ITI for physical fitness and overall development of students.',
    status: 'PUBLISHED'
  },
  // Trainee Section
  {
    title: 'Achievements By Trainees',
    slug: 'achievements-by-trainees',
    description: 'Outstanding achievements and accomplishments of trainees in academics, sports, and competitions.',
    content: `
      <h2>Achievements By Trainees</h2>
      <h3>Student Achievements and Accomplishments</h3>
      <p>S.N. Pvt. Industrial Training Institute takes pride in the achievements of its trainees in various fields.</p>
      
      <h4>Academic Achievements:</h4>
      <ul>
        <li>High pass percentage in NCVT examinations</li>
        <li>Merit positions in state-level competitions</li>
        <li>Excellence in practical training assessments</li>
        <li>Outstanding performance in trade theory</li>
      </ul>
      
      <h4>Technical Competitions:</h4>
      <ul>
        <li>Participation in skill development competitions</li>
        <li>Awards in technical exhibitions</li>
        <li>Recognition in innovation projects</li>
        <li>Success in inter-institute competitions</li>
      </ul>
      
      <h4>Employment Success:</h4>
      <ul>
        <li>High placement rates in reputed companies</li>
        <li>Successful entrepreneurship ventures</li>
        <li>Government job selections</li>
        <li>Higher education admissions</li>
      </ul>
      
      <h4>Sports and Cultural:</h4>
      <ul>
        <li>District level sports achievements</li>
        <li>Cultural program participations</li>
        <li>Leadership roles in student activities</li>
        <li>Community service initiatives</li>
      </ul>
      
      <p>The institute continues to support and encourage students to achieve excellence in all areas of development.</p>
    `,
    metaTitle: 'Student Achievements - S.N. Pvt. ITI',
    metaDesc: 'Outstanding achievements and accomplishments of trainees at S.N. Pvt. ITI in academics, technical competitions, and employment.',
    status: 'PUBLISHED'
  },
  {
    title: 'Records of Trainees',
    slug: 'records-of-trainees',
    description: 'Comprehensive records and documentation of trainee enrollment, progress, and completion details.',
    content: `
      <h2>Records of Trainees</h2>
      <h3>Trainee Record Management</h3>
      <p>S.N. Pvt. Industrial Training Institute maintains comprehensive records of all trainees for proper tracking and documentation.</p>
      
      <h4>Enrollment Records:</h4>
      <ul>
        <li>Student admission details</li>
        <li>Personal and academic information</li>
        <li>Document verification records</li>
        <li>Fee payment history</li>
        <li>Course allocation details</li>
      </ul>
      
      <h4>Academic Records:</h4>
      <ul>
        <li>Attendance records</li>
        <li>Assessment and examination results</li>
        <li>Practical training evaluations</li>
        <li>Progress reports</li>
        <li>Certificate issuance records</li>
      </ul>
      
      <h4>Training Progress:</h4>
      <ul>
        <li>Skill development tracking</li>
        <li>Workshop performance records</li>
        <li>Project completion status</li>
        <li>Industry exposure details</li>
        <li>Placement preparation records</li>
      </ul>
      
      <h4>Record Maintenance:</h4>
      <ul>
        <li>Digital and physical record keeping</li>
        <li>Regular updates and verification</li>
        <li>Secure storage and backup</li>
        <li>Easy retrieval system</li>
        <li>Confidentiality and privacy protection</li>
      </ul>
      
      <p>All trainee records are maintained as per NCVT guidelines and institutional policies.</p>
    `,
    metaTitle: 'Trainee Records - S.N. Pvt. ITI',
    metaDesc: 'Comprehensive trainee record management system at S.N. Pvt. ITI including enrollment, academic, and progress tracking.',
    status: 'PUBLISHED'
  }
]

async function createAllRemainingPages() {
  try {
    console.log('🚀 Starting comprehensive page creation...')
    
    for (const pageData of allRemainingPages) {
      console.log(`Creating page: ${pageData.title}`)
      
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })
      
      if (existingPage) {
        console.log(`⚠️  Page ${pageData.slug} already exists, skipping...`)
        continue
      }
      
      // Create the page
      await prisma.page.create({
        data: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          content: pageData.content,
          metaTitle: pageData.metaTitle,
          metaDesc: pageData.metaDesc,
          status: pageData.status,
          order: 0,
          createdById: 'cmc1p577i0000fcy4iafh42k2' // <EMAIL>
        }
      })
      
      console.log(`✅ Created: ${pageData.title}`)
    }
    
    console.log('🎉 Comprehensive page creation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error creating pages:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAllRemainingPages()
