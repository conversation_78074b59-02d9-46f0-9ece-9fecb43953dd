import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

interface BackupData {
  users: any[]
  emailAccounts: any[]
  paymentItems: any[]
  payments: any[]
  settings: any[]
  exportedAt: string
}

async function migrateDataToSupabase() {
  try {
    console.log('🚀 Starting data migration to Supabase...')

    // Check if backup file exists
    if (!fs.existsSync('data-backup.json')) {
      console.log('⚠️ No backup file found, creating initial data instead...')
      await createInitialData()
      return
    }

    // Load backup data
    console.log('📦 Loading backup data...')
    const backupData: BackupData = JSON.parse(fs.readFileSync('data-backup.json', 'utf8'))
    console.log(`📊 Found backup from ${backupData.exportedAt}`)

    // Test connection first
    await prisma.$connect()
    console.log('✅ Connected to Supabase')

    // Migrate users
    console.log('👥 Migrating users...')
    for (const user of backupData.users) {
      try {
        await prisma.user.upsert({
          where: { email: user.email },
          update: {
            name: user.name,
            password: user.password,
            role: user.role,
            updatedAt: new Date()
          },
          create: {
            id: user.id,
            email: user.email,
            name: user.name,
            password: user.password,
            role: user.role,
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt)
          }
        })
        console.log(`  ✅ Migrated user: ${user.email}`)
      } catch (error) {
        console.log(`  ⚠️ Failed to migrate user ${user.email}:`, error.message)
      }
    }

    // Migrate email accounts
    console.log('📧 Migrating email accounts...')
    for (const account of backupData.emailAccounts) {
      try {
        await prisma.emailAccount.upsert({
          where: { email: account.email },
          update: {
            password: account.password,
            type: account.type,
            isActive: account.isActive,
            quota: parseInt(account.quota) || **********,
            usedQuota: parseInt(account.usedQuota) || 0,
            firstName: account.firstName,
            lastName: account.lastName,
            displayName: account.displayName,
            department: account.department,
            studentId: account.studentId,
            updatedAt: new Date()
          },
          create: {
            id: account.id,
            email: account.email,
            password: account.password,
            type: account.type,
            isActive: account.isActive,
            quota: parseInt(account.quota) || **********,
            usedQuota: parseInt(account.usedQuota) || 0,
            firstName: account.firstName,
            lastName: account.lastName,
            displayName: account.displayName,
            department: account.department,
            studentId: account.studentId,
            createdAt: new Date(account.createdAt),
            updatedAt: new Date(account.updatedAt)
          }
        })
        console.log(`  ✅ Migrated email account: ${account.email}`)
      } catch (error) {
        console.log(`  ⚠️ Failed to migrate email account ${account.email}:`, error.message)
      }
    }

    // Migrate payment catalog items
    console.log('💰 Migrating payment catalog items...')
    for (const item of backupData.paymentItems) {
      try {
        await prisma.paymentCatalogItem.upsert({
          where: { id: item.id },
          update: {
            name: item.name,
            description: item.description,
            feeType: item.feeType,
            amount: parseFloat(item.amount),
            currency: item.currency,
            isActive: item.isActive,
            applicableFor: item.applicableFor,
            updatedAt: new Date()
          },
          create: {
            id: item.id,
            name: item.name,
            description: item.description,
            feeType: item.feeType,
            amount: parseFloat(item.amount),
            currency: item.currency,
            isActive: item.isActive,
            applicableFor: item.applicableFor,
            createdAt: new Date(item.createdAt),
            updatedAt: new Date(item.updatedAt)
          }
        })
        console.log(`  ✅ Migrated payment item: ${item.name}`)
      } catch (error) {
        console.log(`  ⚠️ Failed to migrate payment item ${item.name}:`, error.message)
      }
    }

    console.log('🎉 Data migration completed successfully!')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function createInitialData() {
  try {
    console.log('🆕 Creating initial data for Supabase...')

    // Create admin user
    console.log('👤 Creating admin user...')
    const adminPassword = await bcrypt.hash('Navsaharan89@', 12)
    
    await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: adminPassword,
        updatedAt: new Date()
      },
      create: {
        email: '<EMAIL>',
        name: 'System Administrator',
        password: adminPassword,
        role: 'ADMIN'
      }
    })

    // Create student email account
    console.log('📧 Creating student email account...')
    const studentPassword = await bcrypt.hash('password123', 12)
    
    await prisma.emailAccount.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: studentPassword,
        updatedAt: new Date()
      },
      create: {
        email: '<EMAIL>',
        password: studentPassword,
        type: 'STUDENT',
        isActive: true,
        displayName: 'Student One',
        department: 'Computer Science',
        studentId: 'CS001'
      }
    })

    // Create payment catalog items
    console.log('💰 Creating payment catalog items...')
    const paymentItems = [
      {
        name: 'Semester Fee',
        description: 'Regular semester tuition fee',
        feeType: 'SEMESTER',
        amount: 15000,
        currency: 'INR'
      },
      {
        name: 'Exam Fee',
        description: 'End semester examination fee',
        feeType: 'EXAM',
        amount: 2000,
        currency: 'INR'
      },
      {
        name: 'Library Fee',
        description: 'Annual library access fee',
        feeType: 'LIBRARY',
        amount: 1000,
        currency: 'INR'
      }
    ]

    for (const item of paymentItems) {
      await prisma.paymentCatalogItem.upsert({
        where: { name: item.name },
        update: {
          description: item.description,
          amount: item.amount,
          updatedAt: new Date()
        },
        create: {
          name: item.name,
          description: item.description,
          feeType: item.feeType as any,
          amount: item.amount,
          currency: item.currency,
          isActive: true,
          applicableFor: 'STUDENT'
        }
      })
      console.log(`  ✅ Created payment item: ${item.name}`)
    }

    console.log('✅ Initial data created successfully!')

  } catch (error) {
    console.error('❌ Initial data creation failed:', error)
    throw error
  }
}

migrateDataToSupabase()
  .then(() => {
    console.log('🎯 Migration process completed!')
    console.log('')
    console.log('📋 Next steps:')
    console.log('1. Run the SQL migration script in Supabase SQL Editor')
    console.log('2. Test authentication with the new database')
    console.log('3. Verify all API endpoints work correctly')
    console.log('')
    console.log('🔑 Test Credentials:')
    console.log('   Admin: <EMAIL> / Navsaharan89@')
    console.log('   Student: <EMAIL> / password123')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Migration process failed:', error.message)
    process.exit(1)
  })
