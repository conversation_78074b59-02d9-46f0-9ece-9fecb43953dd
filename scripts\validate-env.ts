import { validateEnvironmentVariables, logEnvironmentStatus } from '../src/lib/env-validation'

async function validateEnvironment() {
  console.log('🔍 Validating environment variables for production deployment...')
  console.log('')

  const validation = validateEnvironmentVariables()

  console.log('📊 Environment Validation Report:')
  console.log('================================')
  console.log('')

  // Required Variables Status
  console.log('🔑 Required Variables:')
  const requiredVars = [
    'DATABASE_URL',
    'SUPABASE_URL', 
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'ADMIN_EMAIL',
    'ADMIN_PASSWORD'
  ]

  requiredVars.forEach(varName => {
    const value = process.env[varName]
    const status = value ? '✅' : '❌'
    const display = value ? (varName.includes('SECRET') || varName.includes('PASSWORD') ? '[HIDDEN]' : value) : 'NOT SET'
    console.log(`  ${status} ${varName}: ${display}`)
  })

  console.log('')

  // Optional Variables Status
  console.log('🔧 Optional Variables:')
  const optionalVars = [
    'SITE_NAME',
    'SITE_URL',
    'EMAIL_DOMAIN',
    'EMAIL_ADMIN',
    'RAZORPAY_KEY_ID',
    'STRIPE_PUBLISHABLE_KEY'
  ]

  optionalVars.forEach(varName => {
    const value = process.env[varName]
    const status = value ? '✅' : '⚪'
    const display = value || 'NOT SET'
    console.log(`  ${status} ${varName}: ${display}`)
  })

  console.log('')

  // Errors
  if (validation.errors.length > 0) {
    console.log('❌ Validation Errors:')
    validation.errors.forEach(error => console.log(`  - ${error}`))
    console.log('')
  }

  // Warnings
  if (validation.warnings.length > 0) {
    console.log('⚠️ Warnings:')
    validation.warnings.forEach(warning => console.log(`  - ${warning}`))
    console.log('')
  }

  // Summary
  console.log('📋 Summary:')
  console.log(`  Status: ${validation.isValid ? '✅ READY FOR DEPLOYMENT' : '❌ NEEDS ATTENTION'}`)
  console.log(`  Errors: ${validation.errors.length}`)
  console.log(`  Warnings: ${validation.warnings.length}`)
  console.log('')

  // Deployment readiness
  if (validation.isValid) {
    console.log('🚀 Environment is ready for Vercel deployment!')
    console.log('')
    console.log('Next steps:')
    console.log('1. Run: npm run build (to verify build works)')
    console.log('2. Run: vercel --prod (to deploy to production)')
    console.log('3. Set environment variables in Vercel dashboard')
    console.log('')
    console.log('🔗 Vercel Environment Variables:')
    console.log('https://vercel.com/dashboard/[your-project]/settings/environment-variables')
  } else {
    console.log('🛠️ Please fix the errors above before deploying to production.')
    console.log('')
    console.log('💡 Quick fixes:')
    if (validation.errors.some(e => e.includes('DATABASE_URL'))) {
      console.log('- Set DATABASE_URL to your Supabase connection string')
    }
    if (validation.errors.some(e => e.includes('NEXTAUTH_SECRET'))) {
      console.log('- Generate a new NEXTAUTH_SECRET: openssl rand -base64 32')
    }
    if (validation.errors.some(e => e.includes('NEXTAUTH_URL'))) {
      console.log('- Set NEXTAUTH_URL to your production domain')
    }
  }

  process.exit(validation.isValid ? 0 : 1)
}

validateEnvironment().catch(error => {
  console.error('💥 Environment validation failed:', error)
  process.exit(1)
})
