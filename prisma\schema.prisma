// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management for admin panel
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(EDITOR)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdPages    Page[]
  createdMedia    Media[]
  createdContents Content[]
  createdEmailAccounts EmailAccount[]
  sentEmails      Email[]    @relation("SentEmails")
  receivedEmails  Email[]    @relation("ReceivedEmails")
  payments        Payment[]  @relation("UserPayments")

  @@map("users")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Page management
model Page {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  description String?
  content     String?
  metaTitle   String?
  metaDesc    String?
  status      PageStatus  @default(DRAFT)
  parentId    String?
  order       Int         @default(0)
  navigationCategory String? // Navigation dropdown assignment (e.g., "About Us", "Admissions", etc.)
  navigationOrder Int @default(0) // Order within navigation category
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy User    @relation(fields: [createdById], references: [id])
  parent    Page?   @relation("PageHierarchy", fields: [parentId], references: [id])
  children  Page[]  @relation("PageHierarchy")
  contents  Content[]

  @@map("pages")
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Content blocks for flexible page content
model Content {
  id      String      @id @default(cuid())
  type    ContentType
  title   String?
  content String?
  data    String? // JSON data for complex content
  order   Int         @default(0)
  pageId  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  createdById String

  // Relations
  page      Page @relation(fields: [pageId], references: [id], onDelete: Cascade)
  createdBy User @relation(fields: [createdById], references: [id])

  @@map("contents")
}

enum ContentType {
  TEXT
  HTML
  IMAGE
  GALLERY
  SLIDESHOW
  TABLE
  LIST
  CONTACT_INFO
  DOWNLOAD
  FEEDBACK_FORM
}

// Media management
model Media {
  id          String       @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  alt         String?
  caption     String?
  category    MediaCategory @default(GENERAL)
  tags        String?      // Comma-separated tags
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  createdById String

  // Relations
  createdBy User @relation(fields: [createdById], references: [id])

  @@map("media")
}

enum MediaCategory {
  GENERAL
  FACULTY
  INFRASTRUCTURE
  EVENTS
  GALLERY
  DOCUMENTS
  CERTIFICATES
  ACHIEVEMENTS
}

// Contact messages
model ContactMessage {
  id        String   @id @default(cuid())
  name      String
  email     String
  contact   String?
  subject   String?
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("contact_messages")
}

// Faculty management
model Faculty {
  id          String   @id @default(cuid())
  name        String
  designation String
  department  String?
  email       String?
  phone       String?
  photoUrl    String?
  bio         String?
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("faculty")
}

// Site settings and configuration
model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  SettingType @default(STRING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

enum SettingType {
  STRING
  NUMBER
  BOOLEAN
  JSON
  COLOR
}

// Navigation menu management
model MenuItem {
  id       String  @id @default(cuid())
  title    String
  url      String?
  pageId   String?
  parentId String?
  order    Int     @default(0)
  isActive Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  parent   MenuItem?   @relation("MenuHierarchy", fields: [parentId], references: [id])
  children MenuItem[]  @relation("MenuHierarchy")

  @@map("menu_items")
}

// Enhanced Navigation management with full hierarchy support
model NavigationItem {
  id          String  @id @default(cuid())
  title       String
  href        String?
  parentId    String?
  order       Int     @default(0)
  isVisible   Boolean @default(true)
  linkType    String  @default("internal") // internal, external, dropdown
  target      String  @default("_self") // _self, _blank
  description String?
  icon        String?
  cssClass    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   NavigationItem?   @relation("NavigationHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children NavigationItem[]  @relation("NavigationHierarchy")

  @@index([parentId])
  @@index([order])
  @@index([isVisible])
  @@map("navigation_items")
}

// Analytics and tracking
model PageView {
  id        String   @id @default(cuid())
  pageSlug  String
  userAgent String?
  ipAddress String?
  referer   String?
  createdAt DateTime @default(now())

  @@map("page_views")
}

// ============================================================================
// EMAIL SYSTEM MODELS
// ============================================================================

// Email accounts management
model EmailAccount {
  id          String      @id @default(cuid())
  email       String      @unique // format: studentid@institute.<NAME_EMAIL>
  password    String      // Hashed password
  type        AccountType @default(STUDENT)
  isActive    Boolean     @default(true)
  quota       BigInt      @default(**********) // 1GB in bytes
  usedQuota   BigInt      @default(0)

  // Account details
  firstName   String?
  lastName    String?
  displayName String?
  department  String?
  studentId   String?     // For student accounts

  // Settings
  signature   String?
  autoReply   Boolean     @default(false)
  autoReplyMessage String?
  forwardingEmail String?

  // Security
  lastLogin   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil DateTime?

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String

  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])
  sentEmails  Email[]     @relation("SentEmails")
  receivedEmails EmailRecipient[]
  folders     EmailFolder[]
  payments    Payment[]
  sessions    EmailSession[]

  @@map("email_accounts")
}

enum AccountType {
  STUDENT     // <EMAIL>
  INSTITUTE   // <EMAIL>
  ADMIN       // <EMAIL>
}

// Email messages
model Email {
  id          String      @id @default(cuid())
  messageId   String      @unique // RFC 5322 Message-ID
  subject     String
  body        String      // HTML content
  bodyText    String?     // Plain text version

  // Email headers
  fromEmail   String
  fromName    String?
  replyTo     String?

  // Email status
  status      EmailStatus @default(DRAFT)
  priority    Priority    @default(NORMAL)
  isRead      Boolean     @default(false)
  isStarred   Boolean     @default(false)
  isDeleted   Boolean     @default(false)

  // Threading
  threadId    String?
  inReplyTo   String?     // Message-ID of email being replied to
  references  String?     // Space-separated Message-IDs

  // Delivery tracking
  sentAt      DateTime?
  deliveredAt DateTime?
  readAt      DateTime?

  // Spam detection
  spamScore   Float       @default(0.0)
  isSpam      Boolean     @default(false)

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  sender      EmailAccount @relation("SentEmails", fields: [fromEmail], references: [email])
  senderUser  User?        @relation("SentEmails", fields: [senderUserId], references: [id])
  senderUserId String?
  recipients  EmailRecipient[]
  attachments EmailAttachment[]
  labels      EmailLabel[]
  queueItems  EmailQueue[]

  @@map("emails")
}

enum EmailStatus {
  DRAFT
  QUEUED
  SENDING
  SENT
  DELIVERED
  FAILED
  BOUNCED
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// Email recipients (To, CC, BCC)
model EmailRecipient {
  id        String        @id @default(cuid())
  emailId   String
  email     String
  name      String?
  type      RecipientType @default(TO)

  // Delivery status
  status    DeliveryStatus @default(PENDING)
  deliveredAt DateTime?
  readAt    DateTime?

  // Relations
  emailMessage Email        @relation(fields: [emailId], references: [id], onDelete: Cascade)
  account   EmailAccount?  @relation(fields: [email], references: [email])

  @@map("email_recipients")
}

enum RecipientType {
  TO
  CC
  BCC
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  BOUNCED
  REJECTED
}

// Email attachments
model EmailAttachment {
  id          String   @id @default(cuid())
  emailId     String
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String   // Supabase storage URL

  createdAt   DateTime @default(now())

  // Relations
  email       Email    @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_attachments")
}

// Email folders (Inbox, Sent, Drafts, Custom folders)
model EmailFolder {
  id          String   @id @default(cuid())
  accountId   String
  name        String
  type        FolderType @default(CUSTOM)
  color       String?
  order       Int      @default(0)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)
  labels      EmailLabel[]

  @@unique([accountId, name])
  @@map("email_folders")
}

enum FolderType {
  INBOX
  SENT
  DRAFTS
  TRASH
  SPAM
  ARCHIVE
  CUSTOM
}

// Email labels/tags
model EmailLabel {
  id        String   @id @default(cuid())
  emailId   String
  folderId  String?
  name      String
  color     String?

  // Relations
  email     Email    @relation(fields: [emailId], references: [id], onDelete: Cascade)
  folder    EmailFolder? @relation(fields: [folderId], references: [id], onDelete: SetNull)

  @@map("email_labels")
}

// ============================================================================
// PAYMENT SYSTEM MODELS
// ============================================================================

// Payment transactions
model Payment {
  id              String        @id @default(cuid())
  transactionId   String        @unique
  studentId       String

  // Payment details
  amount          Float
  currency        String        @default("INR")
  feeType         FeeType
  description     String

  // Gateway details
  gateway         PaymentGateway
  gatewayTxnId    String?       // Gateway transaction ID
  gatewayResponse String?       // JSON response from gateway

  // Additional fees
  additionalFee   Float         @default(0.0)
  totalAmount     Float         // amount + additionalFee

  // Status tracking
  status          PaymentStatus @default(PENDING)
  failureReason   String?

  // Receipt
  receiptNumber   String?       @unique
  receiptUrl      String?       // PDF receipt URL

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  paidAt          DateTime?

  // Relations
  student         EmailAccount  @relation(fields: [studentId], references: [id])
  user            User?         @relation("UserPayments", fields: [userId], references: [id])
  userId          String?

  @@map("payments")
}

enum FeeType {
  ADMISSION
  SEMESTER
  EXAM
  LIBRARY
  HOSTEL
  TRANSPORT
  MISCELLANEOUS
}

enum PaymentGateway {
  PAYU
  PHONEPE
  CASHFREE
}

enum PaymentStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  CANCELLED
  REFUNDED
}

// Payment gateway configurations
model PaymentGatewayConfig {
  id          String         @id @default(cuid())
  gateway     PaymentGateway @unique
  isEnabled   Boolean        @default(false)
  isTestMode  Boolean        @default(true)

  // Configuration JSON (encrypted)
  config      String         // Encrypted gateway credentials

  // Additional fees
  feeType     FeeCalculationType @default(PERCENTAGE)
  feeAmount   Float          @default(0.0)

  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  @@map("payment_gateway_configs")
}

enum FeeCalculationType {
  PERCENTAGE
  FIXED
}

// ============================================================================
// EMAIL PROTOCOL SIMULATION MODELS
// ============================================================================

// SMTP/IMAP session tracking
model EmailSession {
  id          String      @id @default(cuid())
  accountId   String
  sessionType SessionType
  clientInfo  String?     // User agent/client info
  ipAddress   String?

  // Session data
  isActive    Boolean     @default(true)
  lastActivity DateTime   @default(now())

  createdAt   DateTime    @default(now())

  // Relations
  account     EmailAccount @relation(fields: [accountId], references: [id])

  @@map("email_sessions")
}

enum SessionType {
  SMTP
  IMAP
  POP3
  WEBMAIL
}

// Email delivery queue
model EmailQueue {
  id          String      @id @default(cuid())
  emailId     String
  recipientEmail String

  // Queue status
  status      QueueStatus @default(PENDING)
  attempts    Int         @default(0)
  maxAttempts Int         @default(3)

  // Scheduling
  scheduledAt DateTime    @default(now())
  nextRetry   DateTime?

  // Error tracking
  lastError   String?

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  email       Email       @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_queue")
}

enum QueueStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  CANCELLED
}

// ============================================================================
// SYSTEM CONFIGURATION MODELS
// ============================================================================

// Email server settings
model EmailServerConfig {
  id          String   @id @default(cuid())
  domain      String   @unique // institute.edu

  // SMTP settings
  smtpHost    String   @default("mail.institute.edu")
  smtpPort    Int      @default(587)
  smtpSecurity String  @default("STARTTLS")

  // IMAP settings
  imapHost    String   @default("mail.institute.edu")
  imapPort    Int      @default(993)
  imapSecurity String  @default("SSL")

  // POP3 settings
  pop3Host    String   @default("mail.institute.edu")
  pop3Port    Int      @default(995)
  pop3Security String  @default("SSL")

  // Quotas and limits
  defaultQuota BigInt  @default(**********) // 1GB
  maxAttachmentSize Int @default(26214400) // 25MB

  // Anti-spam settings
  spamThreshold Float  @default(5.0)
  enableSpamFilter Boolean @default(true)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("email_server_configs")
}
