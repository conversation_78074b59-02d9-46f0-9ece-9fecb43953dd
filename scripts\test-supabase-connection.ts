import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testConnection() {
  try {
    console.log('🔌 Testing Supabase connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Connected to Supabase successfully!')
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Query test successful:', result)
    
    // Check if any tables exist
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `
    console.log('📊 Existing tables:', tables)
    
  } catch (error) {
    console.error('❌ Connection failed:', error)
    
    if (error.code === 'P1001') {
      console.log('\n💡 Connection troubleshooting:')
      console.log('1. Check if DATABASE_URL is correct')
      console.log('2. Verify Supabase project is active')
      console.log('3. Check if IP is whitelisted (if applicable)')
      console.log('4. Verify database password is correct')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
  .then(() => {
    console.log('🎉 Connection test completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Connection test failed:', error.message)
    process.exit(1)
  })
