const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSamplePayments() {
  try {
    console.log('Creating sample payment data...')

    // Get existing users and catalog items
    const users = await prisma.emailAccount.findMany({
      where: { type: 'STUDENT' },
      take: 5
    })

    const catalogItems = await prisma.paymentCatalogItem.findMany({
      take: 2
    })

    if (users.length === 0) {
      console.log('No student users found. Creating sample student...')
      
      // Create a sample student
      const sampleStudent = await prisma.emailAccount.create({
        data: {
          email: '<EMAIL>',
          password: '$2b$12$LQv3c1yqBwlVHpPjrEYHNOyHDxdWGGJViyplkcRForhZvlM3jG/Su', // password123
          type: 'STUDENT',
          displayName: 'Sample Student',
          firstName: 'Sample',
          lastName: 'Student',
          isActive: true,
          quota: 1024 * 1024 * 1024, // 1GB
          createdById: 'cmd1yio8x000hfc3sh1dmmb7n' // Admin user ID
        }
      })
      
      users.push(sampleStudent)
    }

    if (catalogItems.length === 0) {
      console.log('No catalog items found. Please run the seed script first.')
      return
    }

    // Create sample payment transactions
    const samplePayments = []
    const statuses = ['PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED']
    const gateways = ['RAZORPAY', 'STRIPE', 'PAYU', 'CASHFREE']
    const paymentMethods = ['UPI', 'Credit Card', 'Debit Card', 'Net Banking', 'Wallet']

    for (let i = 0; i < 20; i++) {
      const user = users[Math.floor(Math.random() * users.length)]
      const catalogItem = catalogItems[Math.floor(Math.random() * catalogItems.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const gateway = gateways[Math.floor(Math.random() * gateways.length)]
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)]
      
      const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date in last 30 days
      const completedAt = status === 'SUCCESS' ? new Date(createdAt.getTime() + Math.random() * 60 * 60 * 1000) : null // Complete within 1 hour if successful

      const baseAmount = catalogItem.amount
      const additionalFee = Math.random() * 50 // Random additional fee
      const totalAmount = baseAmount + additionalFee

      samplePayments.push({
        transactionId: `TXN${Date.now()}${i.toString().padStart(3, '0')}`,
        studentId: user.id,
        catalogItemId: catalogItem.id,
        amount: baseAmount,
        additionalFee: additionalFee,
        totalAmount: totalAmount,
        currency: 'INR',
        feeType: catalogItem.feeType,
        description: `Payment for ${catalogItem.name}`,
        status: status,
        gateway: gateway,
        gatewayTxnId: `${gateway}_${Date.now()}_${i}`,
        gatewayResponse: JSON.stringify({
          gateway_payment_id: `pay_${Date.now()}_${i}`,
          method: paymentMethod.toLowerCase().replace(' ', '_'),
          status: status.toLowerCase()
        }),
        createdAt: createdAt,
        completedAt: completedAt
      })
    }

    // Insert sample payments
    for (const payment of samplePayments) {
      await prisma.payment.create({
        data: payment
      })
    }

    console.log(`✅ Created ${samplePayments.length} sample payment transactions`)
    
    // Display summary
    const summary = await prisma.payment.groupBy({
      by: ['status'],
      _count: {
        status: true
      },
      _sum: {
        totalAmount: true
      }
    })

    console.log('\n📊 Payment Summary:')
    summary.forEach(item => {
      console.log(`${item.status}: ${item._count.status} transactions, ₹${item._sum.totalAmount?.toFixed(2) || 0}`)
    })

  } catch (error) {
    console.error('Error creating sample payments:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSamplePayments()
