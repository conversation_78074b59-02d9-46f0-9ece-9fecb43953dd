/* eslint-disable @typescript-eslint/no-explicit-any */
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./prisma"
import type { AuthOptions } from "next-auth"

export const authOptions: AuthOptions = {
  providers: [
    // Admin credentials provider
    CredentialsProvider({
      id: "admin-credentials",
      name: "Admin Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Skip authentication during build time
        if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production' && !prisma) {
          return null
        }

        try {
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (!user) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          )

          if (process.env.NODE_ENV === 'development') {
            console.log("Admin auth debug:", {
              email: credentials.email,
              userFound: !!user,
              passwordValid: isPasswordValid
            })
          }

          if (!isPasswordValid) {
            return null
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            userType: 'ADMIN'
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Admin authentication error:", error)
          }
          return null
        }
      }
    }),
    // Student credentials provider
    CredentialsProvider({
      id: "student-credentials",
      name: "Student Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Skip authentication during build time
        if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production' && !prisma) {
          return null
        }

        try {
          const emailAccount = await prisma.emailAccount.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (!emailAccount || !emailAccount.isActive) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            emailAccount.password
          )

          if (!isPasswordValid) {
            return null
          }

          // Update last login
          await prisma.emailAccount.update({
            where: { id: emailAccount.id },
            data: {
              lastLogin: new Date(),
              loginAttempts: 0 // Reset failed attempts on successful login
            }
          })

          return {
            id: emailAccount.id,
            email: emailAccount.email,
            name: emailAccount.displayName || emailAccount.email.split('@')[0],
            role: emailAccount.type,
            userType: 'STUDENT'
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Student authentication error:", error)
          }
          return null
        }
      }
    }),
    // Legacy credentials provider for backward compatibility
    CredentialsProvider({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Skip authentication during build time
        if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production' && !prisma) {
          return null
        }

        try {
          // Try admin user first
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (user) {
            const isPasswordValid = await bcrypt.compare(
              credentials.password,
              user.password
            )

            if (isPasswordValid) {
              return {
                id: user.id,
                email: user.email,
                name: user.name,
                role: user.role,
                userType: 'ADMIN'
              }
            }
          }

          // Try email account if admin user not found
          const emailAccount = await prisma.emailAccount.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (emailAccount && emailAccount.isActive) {
            const isPasswordValid = await bcrypt.compare(
              credentials.password,
              emailAccount.password
            )

            if (isPasswordValid) {
              // Update last login
              await prisma.emailAccount.update({
                where: { id: emailAccount.id },
                data: {
                  lastLogin: new Date(),
                  loginAttempts: 0
                }
              })

              return {
                id: emailAccount.id,
                email: emailAccount.email,
                name: emailAccount.displayName || emailAccount.email.split('@')[0],
                role: emailAccount.type,
                userType: 'STUDENT'
              }
            }
          }

          return null
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error("Authentication error:", error)
          }
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }: { token: any; user: any }) {
      if (user) {
        token.role = user.role
        token.id = user.id
        token.userType = user.userType
      }
      return token
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id || token.sub!
        session.user.role = token.role as string
        session.user.userType = token.userType as string
      }
      return session
    },
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      // Handle role-based redirects
      if (url.includes('callbackUrl')) {
        const urlObj = new URL(url, baseUrl)
        const callbackUrl = urlObj.searchParams.get('callbackUrl')
        if (callbackUrl) {
          return callbackUrl
        }
      }

      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    }
  },
  pages: {
    signIn: "/admin/login", // Default sign-in page
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: false,
}
