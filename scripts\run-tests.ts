#!/usr/bin/env tsx

/**
 * Comprehensive Test Runner for SNPITC Email & Payment Integration
 * 
 * Runs all test suites including unit tests, integration tests, and E2E tests
 * with proper setup, teardown, and reporting.
 */

import { execSync, spawn } from 'child_process'
import fs from 'fs'
import path from 'path'

interface TestSuite {
  name: string
  command: string
  description: string
  required: boolean
  timeout: number
}

interface TestResult {
  suite: string
  passed: boolean
  duration: number
  output: string
  error?: string
}

class TestRunner {
  private results: TestResult[] = []
  private startTime: number = Date.now()

  private testSuites: TestSuite[] = [
    {
      name: 'Unit Tests',
      command: 'npm run test -- --coverage --passWithNoTests',
      description: 'Run unit tests for API endpoints and components',
      required: true,
      timeout: 300000 // 5 minutes
    },
    {
      name: 'Integration Tests',
      command: 'npm run test -- tests/integration --passWithNoTests',
      description: 'Run integration tests for database and system integration',
      required: false, // Optional if no test database
      timeout: 600000 // 10 minutes
    },
    {
      name: 'E2E Tests',
      command: 'npx playwright test',
      description: 'Run end-to-end tests for admin panel functionality',
      required: false, // Optional in CI
      timeout: 900000 // 15 minutes
    },
    {
      name: 'Type Checking',
      command: 'npx tsc --noEmit',
      description: 'Check TypeScript types and compilation',
      required: true,
      timeout: 120000 // 2 minutes
    },
    {
      name: 'Linting',
      command: 'npm run lint',
      description: 'Run ESLint for code quality checks',
      required: true,
      timeout: 60000 // 1 minute
    }
  ]

  async runAllTests(): Promise<boolean> {
    console.log('🚀 Starting comprehensive test suite for SNPITC Email & Payment Integration...\n')

    // Setup test environment
    await this.setupTestEnvironment()

    let allPassed = true

    for (const suite of this.testSuites) {
      console.log(`📋 Running ${suite.name}...`)
      console.log(`   ${suite.description}`)

      const result = await this.runTestSuite(suite)
      this.results.push(result)

      if (result.passed) {
        console.log(`✅ ${suite.name} passed (${result.duration}ms)\n`)
      } else {
        console.log(`❌ ${suite.name} failed (${result.duration}ms)`)
        if (result.error) {
          console.log(`   Error: ${result.error}`)
        }
        console.log('')

        if (suite.required) {
          allPassed = false
        }
      }
    }

    // Generate test report
    await this.generateTestReport()

    // Cleanup test environment
    await this.cleanupTestEnvironment()

    this.printSummary(allPassed)
    return allPassed
  }

  private async setupTestEnvironment(): Promise<void> {
    console.log('🔧 Setting up test environment...')

    try {
      // Ensure test directories exist
      const testDirs = ['test-results', 'coverage', 'playwright-report']
      for (const dir of testDirs) {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true })
        }
      }

      // Set test environment variables
      process.env.NODE_ENV = 'test'
      process.env.NEXTAUTH_SECRET = 'test-secret'
      process.env.NEXTAUTH_URL = 'http://localhost:3000'

      // Check if test database is available
      if (process.env.TEST_DATABASE_URL) {
        console.log('   ✅ Test database configured')
      } else {
        console.log('   ⚠️  Test database not configured (integration tests will be skipped)')
      }

      // Install Playwright browsers if needed
      if (this.shouldRunE2ETests()) {
        try {
          execSync('npx playwright install --with-deps', { stdio: 'pipe' })
          console.log('   ✅ Playwright browsers installed')
        } catch (error) {
          console.log('   ⚠️  Playwright browser installation failed')
        }
      }

      console.log('✅ Test environment setup complete\n')
    } catch (error) {
      console.error('❌ Test environment setup failed:', error)
      throw error
    }
  }

  private async runTestSuite(suite: TestSuite): Promise<TestResult> {
    const startTime = Date.now()

    try {
      // Skip optional tests based on environment
      if (!suite.required && this.shouldSkipSuite(suite)) {
        return {
          suite: suite.name,
          passed: true,
          duration: Date.now() - startTime,
          output: 'Skipped (not required in current environment)'
        }
      }

      const output = execSync(suite.command, {
        encoding: 'utf8',
        timeout: suite.timeout,
        env: { ...process.env, CI: 'true' }
      })

      return {
        suite: suite.name,
        passed: true,
        duration: Date.now() - startTime,
        output
      }
    } catch (error: any) {
      return {
        suite: suite.name,
        passed: false,
        duration: Date.now() - startTime,
        output: error.stdout || '',
        error: error.message || 'Unknown error'
      }
    }
  }

  private shouldSkipSuite(suite: TestSuite): boolean {
    // Skip integration tests if no test database
    if (suite.name === 'Integration Tests' && !process.env.TEST_DATABASE_URL) {
      return true
    }

    // Skip E2E tests in CI unless explicitly enabled
    if (suite.name === 'E2E Tests' && process.env.CI && !process.env.RUN_E2E_TESTS) {
      return true
    }

    return false
  }

  private shouldRunE2ETests(): boolean {
    return !process.env.CI || !!process.env.RUN_E2E_TESTS
  }

  private async generateTestReport(): Promise<void> {
    console.log('📊 Generating test report...')

    const report = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        ci: !!process.env.CI,
        testDatabase: !!process.env.TEST_DATABASE_URL
      },
      results: this.results,
      summary: {
        total: this.results.length,
        passed: this.results.filter(r => r.passed).length,
        failed: this.results.filter(r => !r.passed).length,
        skipped: this.results.filter(r => r.output.includes('Skipped')).length
      }
    }

    const reportPath = path.join('test-results', 'test-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    // Generate HTML report
    const htmlReport = this.generateHTMLReport(report)
    const htmlReportPath = path.join('test-results', 'test-report.html')
    fs.writeFileSync(htmlReportPath, htmlReport)

    console.log(`✅ Test report generated: ${reportPath}`)
    console.log(`✅ HTML report generated: ${htmlReportPath}`)
  }

  private generateHTMLReport(report: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>SNPITC Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center; }
        .passed { background: #e8f5e8; }
        .failed { background: #ffebee; }
        .test-result { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-passed { border-left: 5px solid #4caf50; }
        .test-failed { border-left: 5px solid #f44336; }
        .test-skipped { border-left: 5px solid #ff9800; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SNPITC Email & Payment Integration Test Report</h1>
        <p>Generated: ${report.timestamp}</p>
        <p>Duration: ${Math.round(report.duration / 1000)}s</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>${report.summary.total}</h3>
            <p>Total Tests</p>
        </div>
        <div class="metric passed">
            <h3>${report.summary.passed}</h3>
            <p>Passed</p>
        </div>
        <div class="metric failed">
            <h3>${report.summary.failed}</h3>
            <p>Failed</p>
        </div>
        <div class="metric">
            <h3>${report.summary.skipped}</h3>
            <p>Skipped</p>
        </div>
    </div>
    
    <h2>Test Results</h2>
    ${report.results.map((result: TestResult) => `
        <div class="test-result test-${result.passed ? 'passed' : 'failed'}">
            <h3>${result.suite} ${result.passed ? '✅' : '❌'}</h3>
            <p>Duration: ${result.duration}ms</p>
            ${result.error ? `<p style="color: red;">Error: ${result.error}</p>` : ''}
        </div>
    `).join('')}
    
    <div style="margin-top: 40px; padding: 20px; background: #f5f5f5; border-radius: 5px;">
        <h3>Environment</h3>
        <ul>
            <li>Node.js: ${report.environment.nodeVersion}</li>
            <li>Platform: ${report.environment.platform}</li>
            <li>CI: ${report.environment.ci ? 'Yes' : 'No'}</li>
            <li>Test Database: ${report.environment.testDatabase ? 'Configured' : 'Not configured'}</li>
        </ul>
    </div>
</body>
</html>
    `
  }

  private async cleanupTestEnvironment(): Promise<void> {
    console.log('🧹 Cleaning up test environment...')
    
    try {
      // Clean up any test artifacts
      // Note: Keep test results for review
      console.log('✅ Test environment cleanup complete')
    } catch (error) {
      console.log('⚠️  Test environment cleanup had issues:', error)
    }
  }

  private printSummary(allPassed: boolean): void {
    const totalDuration = Date.now() - this.startTime
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed).length
    const skipped = this.results.filter(r => r.output.includes('Skipped')).length

    console.log('=' .repeat(60))
    console.log('📊 Test Summary')
    console.log('=' .repeat(60))
    console.log(`Total Duration: ${Math.round(totalDuration / 1000)}s`)
    console.log(`Total Suites: ${this.results.length}`)
    console.log(`✅ Passed: ${passed}`)
    console.log(`❌ Failed: ${failed}`)
    console.log(`⏭️  Skipped: ${skipped}`)
    console.log('=' .repeat(60))

    if (allPassed) {
      console.log('🎉 All required tests passed! System is ready for production.')
    } else {
      console.log('⚠️  Some required tests failed. Please review and fix issues before deployment.')
    }

    console.log('\n📋 Next Steps:')
    if (allPassed) {
      console.log('   1. Review test coverage report')
      console.log('   2. Deploy to staging environment')
      console.log('   3. Run production smoke tests')
    } else {
      console.log('   1. Review failed test details')
      console.log('   2. Fix identified issues')
      console.log('   3. Re-run test suite')
    }
  }
}

// Main execution
async function main() {
  const runner = new TestRunner()
  
  try {
    const success = await runner.runAllTests()
    process.exit(success ? 0 : 1)
  } catch (error) {
    console.error('❌ Test runner failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

export { TestRunner }
