const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const navigationData = [
  {
    id: 'nav-home',
    title: 'Home',
    href: '/',
    parentId: null,
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-about',
    title: 'About Us',
    href: null,
    parentId: null,
    order: 1,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-about-institute',
    title: 'About Institute',
    href: '/about-institute',
    parentId: 'nav-about',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-about-intro',
    title: 'Introduction of Institute',
    href: '/introduction-institute',
    parentId: 'nav-about',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-about-scheme',
    title: 'Scheme Running in The Institute',
    href: '/scheme-running',
    parentId: 'nav-about',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions',
    title: 'Admissions',
    href: null,
    parentId: null,
    order: 2,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-admissions-criteria',
    title: 'Admission Criteria',
    href: '/admission-criteria',
    parentId: 'nav-admissions',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions-ncvt-scvt',
    title: 'Trades Affilated To NCVT and SCVT',
    href: '/ncvt-scvt-affilated',
    parentId: 'nav-admissions',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions-ncvt',
    title: 'Summary of Trades Affilated To NCVT',
    href: '/ncvt-affilated',
    parentId: 'nav-admissions',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions-scvt',
    title: 'Summary of Trades Affilated To SCVT',
    href: '/scvt-affilated',
    parentId: 'nav-admissions',
    order: 3,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions-format',
    title: 'Application Format',
    href: '/application-format',
    parentId: 'nav-admissions',
    order: 4,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-admissions-fee',
    title: 'Fee Structure',
    href: '/fee-structure',
    parentId: 'nav-admissions',
    order: 5,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities',
    title: 'Facilities',
    href: null,
    parentId: null,
    order: 3,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-facilities-infra',
    title: 'Infrastructure,Buliding and Workshop',
    href: '/infrastructure',
    parentId: 'nav-facilities',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities-ts',
    title: 'Trade Specific Infrastructure',
    href: '/ts-infrastructure',
    parentId: 'nav-facilities',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities-power',
    title: 'Electric Power Supply',
    href: '/electric-power',
    parentId: 'nav-facilities',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities-library',
    title: 'Library',
    href: '/library',
    parentId: 'nav-facilities',
    order: 3,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities-lab',
    title: 'Computer lab',
    href: '/computer-lab',
    parentId: 'nav-facilities',
    order: 4,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-facilities-sports',
    title: 'Sports',
    href: '/sports',
    parentId: 'nav-facilities',
    order: 5,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee',
    title: 'Trainee',
    href: null,
    parentId: null,
    order: 4,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-trainee-achievements',
    title: 'Achievements By Trainees',
    href: '/achievements-by-trainees',
    parentId: 'nav-trainee',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-records',
    title: 'Records of Trainees',
    href: '/records-of-trainees',
    parentId: 'nav-trainee',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-attendance',
    title: 'Attendance of Trainees',
    href: '/attendance-of-trainee',
    parentId: 'nav-trainee',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-certificates',
    title: 'Certificates Issued To Trainees',
    href: '/certificate-issued',
    parentId: 'nav-trainee',
    order: 3,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-progress',
    title: 'PROGRESS CARD',
    href: '/progress-card',
    parentId: 'nav-trainee',
    order: 4,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-placements',
    title: 'Placements',
    href: '/placements',
    parentId: 'nav-trainee',
    order: 5,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-results',
    title: 'Results',
    href: '/results',
    parentId: 'nav-trainee',
    order: 6,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-energy',
    title: 'Energy Consumption',
    href: '/ee-consumption-pspm',
    parentId: 'nav-trainee',
    order: 7,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-trainee-material',
    title: 'Raw Material Consumption',
    href: '/rm-consumption-pspm',
    parentId: 'nav-trainee',
    order: 8,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-staff',
    title: 'Staff',
    href: null,
    parentId: null,
    order: 5,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-staff-faculty',
    title: 'Faculty',
    href: '/faculty',
    parentId: 'nav-staff',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-staff-admin',
    title: 'Administrative Staff',
    href: '/administrative-staff',
    parentId: 'nav-staff',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-staff-attendance',
    title: 'Attendance of Instructor',
    href: '/attendance-instructor',
    parentId: 'nav-staff',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more',
    title: 'More',
    href: null,
    parentId: null,
    order: 6,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-more-industry',
    title: 'Industry Institute linkage',
    href: '/industry-linkages',
    parentId: 'nav-more',
    order: 0,
    isVisible: true,
    linkType: 'dropdown',
    target: '_self'
  },
  {
    id: 'nav-more-industry-partner',
    title: 'Name of the Industry Partner',
    href: '/industry-partner',
    parentId: 'nav-more-industry',
    order: 0,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-industry-activities',
    title: 'Major Activities / Contributions',
    href: '/major-activities',
    parentId: 'nav-more-industry',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-industry-visit',
    title: 'Industry Visit / Industrial Tour',
    href: '/industry-visit',
    parentId: 'nav-more-industry',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-industry-faculty',
    title: 'Guest Faculty',
    href: '/guest-faculty',
    parentId: 'nav-more-industry',
    order: 3,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-industry-workshop',
    title: 'Workshop & Seminars',
    href: '/workshop-seminar',
    parentId: 'nav-more-industry',
    order: 4,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-activities',
    title: 'Activities',
    href: '/activities',
    parentId: 'nav-more',
    order: 1,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-rti',
    title: 'RTI',
    href: '/rti',
    parentId: 'nav-more',
    order: 2,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-inspection',
    title: 'Inspection Details',
    href: '/inspection-details',
    parentId: 'nav-more',
    order: 3,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-directorate',
    title: 'State Directorate',
    href: '/state-directorate',
    parentId: 'nav-more',
    order: 4,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-iso',
    title: 'Certificate ISO',
    href: '/iso-certificate',
    parentId: 'nav-more',
    order: 5,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-funds',
    title: 'Funds Status',
    href: '/fund-status',
    parentId: 'nav-more',
    order: 6,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-orders',
    title: 'DGET And State Govt. Orders',
    href: '/dget-orders',
    parentId: 'nav-more',
    order: 7,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-rating',
    title: 'Rating Of Institute',
    href: '/ratting',
    parentId: 'nav-more',
    order: 8,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-grievance',
    title: 'Grievance Redressal Mechanism',
    href: '/grm',
    parentId: 'nav-more',
    order: 9,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-more-maintenance',
    title: 'Maintenance Expenditure',
    href: '/building-maintenance',
    parentId: 'nav-more',
    order: 10,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-gallery',
    title: 'Gallery',
    href: '/gallery',
    parentId: null,
    order: 7,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-feedback',
    title: 'Feedback',
    href: '/feedback',
    parentId: null,
    order: 8,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-contact',
    title: 'Contact',
    href: '/contact',
    parentId: null,
    order: 9,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  },
  {
    id: 'nav-sitemap',
    title: 'Site Map',
    href: '/sitemap',
    parentId: null,
    order: 10,
    isVisible: true,
    linkType: 'internal',
    target: '_self'
  }
]

async function populateNavigation() {
  try {
    console.log('🚀 Starting navigation population...')
    
    // Clear existing navigation
    await prisma.navigationItem.deleteMany({})
    console.log('✅ Cleared existing navigation items')
    
    // Insert navigation items
    for (const item of navigationData) {
      await prisma.navigationItem.create({
        data: item
      })
      console.log(`✅ Created: ${item.title}`)
    }
    
    console.log('🎉 Navigation population completed successfully!')
    
    // Verify the data
    const count = await prisma.navigationItem.count()
    console.log(`📊 Total navigation items: ${count}`)
    
  } catch (error) {
    console.error('❌ Error populating navigation:', error)
  } finally {
    await prisma.$disconnect()
  }
}

populateNavigation()
