const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function seedGallery() {
  console.log('🌱 Seeding gallery with downloaded images...')

  // Get the first admin user to assign as creator
  const adminUser = await prisma.user.findFirst({
    where: {
      role: 'ADMIN'
    }
  })

  if (!adminUser) {
    console.error('❌ No admin user found. Please create an admin user first.')
    return
  }

  console.log(`👤 Using admin user: ${adminUser.email}`)

  const galleryImages = [
    {
      filename: 'img1.jpg',
      originalName: 'Campus View 1',
      url: '/images/gallery/img1.jpg',
      mimeType: 'image/jpeg',
      size: 150000,
      alt: 'Campus view showing main building and entrance',
      caption: 'Main campus building with modern infrastructure'
    },
    {
      filename: 'img2.jpg',
      originalName: 'Workshop Area',
      url: '/images/gallery/img2.jpg',
      mimeType: 'image/jpeg',
      size: 180000,
      alt: 'Students working in the electrical workshop',
      caption: 'Electrical workshop with students practicing hands-on training'
    },
    {
      filename: 'img3.jpg',
      originalName: 'Computer Lab',
      url: '/images/gallery/img3.jpg',
      mimeType: 'image/jpeg',
      size: 160000,
      alt: 'Modern computer laboratory with latest equipment',
      caption: 'Well-equipped computer lab for digital learning'
    },
    {
      filename: 'img4.jpg',
      originalName: 'Library Facility',
      url: '/images/gallery/img4.jpg',
      mimeType: 'image/jpeg',
      size: 140000,
      alt: 'Institute library with technical books and resources',
      caption: 'Comprehensive library with technical reference materials'
    },
    {
      filename: 'img5.jpg',
      originalName: 'Training Session',
      url: '/images/gallery/img5.jpg',
      mimeType: 'image/jpeg',
      size: 170000,
      alt: 'Faculty conducting practical training session',
      caption: 'Practical training session with experienced faculty'
    }
  ]

  try {
    // Clear existing gallery images (optional)
    console.log('🗑️  Clearing existing gallery images...')
    await prisma.media.deleteMany({
      where: {
        url: {
          startsWith: '/images/gallery/'
        }
      }
    })

    // Add new gallery images
    console.log('📸 Adding new gallery images...')
    for (const image of galleryImages) {
      await prisma.media.create({
        data: {
          filename: image.filename,
          originalName: image.originalName,
          url: image.url,
          mimeType: image.mimeType,
          size: image.size,
          alt: image.alt,
          caption: image.caption,
          createdById: adminUser.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      console.log(`✅ Added: ${image.originalName}`)
    }

    console.log('🎉 Gallery seeding completed successfully!')
    console.log(`📊 Total images added: ${galleryImages.length}`)

  } catch (error) {
    console.error('❌ Error seeding gallery:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seed function
seedGallery()
  .catch((error) => {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  })
