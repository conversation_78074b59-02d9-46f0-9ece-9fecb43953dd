# Prometheus Configuration for SNPITC Email & Payment System

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Next.js Application
  - job_name: 'snpitc-app'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    metrics_path: '/nginx-status'
    scrape_interval: 30s

  # Node Exporter (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Custom Email System Metrics
  - job_name: 'email-metrics'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/api/admin/metrics/email'
    scrape_interval: 60s
    basic_auth:
      username: 'admin'
      password: 'metrics-password'

  # Custom Payment System Metrics
  - job_name: 'payment-metrics'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/api/admin/metrics/payments'
    scrape_interval: 60s
    basic_auth:
      username: 'admin'
      password: 'metrics-password'

  # Application Health Checks
  - job_name: 'health-checks'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/api/health/detailed'
    scrape_interval: 30s
