{"name": "snpitc-remake", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "build:prod": "prisma generate && NODE_ENV=production next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "postinstall": "prisma generate", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "migrate:supabase": "tsx scripts/migrate-to-supabase.ts", "analyze": "ANALYZE=true npm run build", "vercel-build": "prisma generate && prisma migrate deploy && next build"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.39.0", "@supabase/storage-js": "^2.5.5", "@tiptap/extension-bullet-list": "^2.14.0", "@tiptap/extension-character-count": "^2.14.1", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-font-family": "^2.22.0", "@tiptap/extension-hard-break": "^2.14.1", "@tiptap/extension-highlight": "^2.22.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-ordered-list": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.1", "@tiptap/extension-subscript": "^2.22.0", "@tiptap/extension-superscript": "^2.22.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.22.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.517.0", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "uuid": "^10.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}