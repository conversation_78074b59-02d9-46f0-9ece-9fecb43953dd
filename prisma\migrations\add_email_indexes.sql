-- Add indexes for email system performance optimization

-- Email table indexes
CREATE INDEX IF NOT EXISTS "emails_from_email_idx" ON "emails"("fromEmail");
CREATE INDEX IF NOT EXISTS "emails_created_at_idx" ON "emails"("createdAt" DESC);
CREATE INDEX IF NOT EXISTS "emails_status_idx" ON "emails"("status");
CREATE INDEX IF NOT EXISTS "emails_thread_id_idx" ON "emails"("threadId");
CREATE INDEX IF NOT EXISTS "emails_is_read_idx" ON "emails"("isRead");
CREATE INDEX IF NOT EXISTS "emails_is_starred_idx" ON "emails"("isStarred");
CREATE INDEX IF NOT EXISTS "emails_is_deleted_idx" ON "emails"("isDeleted");
CREATE INDEX IF NOT EXISTS "emails_is_spam_idx" ON "emails"("isSpam");
CREATE INDEX IF NOT EXISTS "emails_sent_at_idx" ON "emails"("sentAt" DESC);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS "emails_from_status_created_idx" ON "emails"("fromEmail", "status", "createdAt" DESC);
CREATE INDEX IF NOT EXISTS "emails_read_starred_created_idx" ON "emails"("isRead", "isStarred", "createdAt" DESC);
CREATE INDEX IF NOT EXISTS "emails_deleted_created_idx" ON "emails"("isDeleted", "createdAt" DESC);

-- Full-text search indexes (PostgreSQL specific)
CREATE INDEX IF NOT EXISTS "emails_subject_gin_idx" ON "emails" USING gin(to_tsvector('english', "subject"));
CREATE INDEX IF NOT EXISTS "emails_body_gin_idx" ON "emails" USING gin(to_tsvector('english', "body"));

-- Email recipients indexes
CREATE INDEX IF NOT EXISTS "email_recipients_email_idx" ON "email_recipients"("email");
CREATE INDEX IF NOT EXISTS "email_recipients_email_id_idx" ON "email_recipients"("emailId");
CREATE INDEX IF NOT EXISTS "email_recipients_type_idx" ON "email_recipients"("type");
CREATE INDEX IF NOT EXISTS "email_recipients_status_idx" ON "email_recipients"("status");

-- Composite index for recipient queries
CREATE INDEX IF NOT EXISTS "email_recipients_email_type_idx" ON "email_recipients"("email", "type");

-- Email attachments indexes
CREATE INDEX IF NOT EXISTS "email_attachments_email_id_idx" ON "email_attachments"("emailId");
CREATE INDEX IF NOT EXISTS "email_attachments_size_idx" ON "email_attachments"("size");
CREATE INDEX IF NOT EXISTS "email_attachments_mime_type_idx" ON "email_attachments"("mimeType");

-- Email folders indexes
CREATE INDEX IF NOT EXISTS "email_folders_account_id_idx" ON "email_folders"("accountId");
CREATE INDEX IF NOT EXISTS "email_folders_type_idx" ON "email_folders"("type");
CREATE INDEX IF NOT EXISTS "email_folders_order_idx" ON "email_folders"("order");

-- Email labels indexes
CREATE INDEX IF NOT EXISTS "email_labels_email_id_idx" ON "email_labels"("emailId");
CREATE INDEX IF NOT EXISTS "email_labels_folder_id_idx" ON "email_labels"("folderId");
CREATE INDEX IF NOT EXISTS "email_labels_name_idx" ON "email_labels"("name");

-- Email queue indexes
CREATE INDEX IF NOT EXISTS "email_queue_status_idx" ON "email_queue"("status");
CREATE INDEX IF NOT EXISTS "email_queue_scheduled_at_idx" ON "email_queue"("scheduledAt");
CREATE INDEX IF NOT EXISTS "email_queue_attempts_idx" ON "email_queue"("attempts");
CREATE INDEX IF NOT EXISTS "email_queue_email_id_idx" ON "email_queue"("emailId");
CREATE INDEX IF NOT EXISTS "email_queue_recipient_email_idx" ON "email_queue"("recipientEmail");

-- Composite index for queue processing
CREATE INDEX IF NOT EXISTS "email_queue_status_scheduled_idx" ON "email_queue"("status", "scheduledAt");

-- Email sessions indexes
CREATE INDEX IF NOT EXISTS "email_sessions_account_id_idx" ON "email_sessions"("accountId");
CREATE INDEX IF NOT EXISTS "email_sessions_session_type_idx" ON "email_sessions"("sessionType");
CREATE INDEX IF NOT EXISTS "email_sessions_is_active_idx" ON "email_sessions"("isActive");
CREATE INDEX IF NOT EXISTS "email_sessions_last_activity_idx" ON "email_sessions"("lastActivity" DESC);

-- Email accounts indexes
CREATE INDEX IF NOT EXISTS "email_accounts_email_idx" ON "email_accounts"("email");
CREATE INDEX IF NOT EXISTS "email_accounts_type_idx" ON "email_accounts"("type");
CREATE INDEX IF NOT EXISTS "email_accounts_is_active_idx" ON "email_accounts"("isActive");
CREATE INDEX IF NOT EXISTS "email_accounts_created_by_id_idx" ON "email_accounts"("createdById");
CREATE INDEX IF NOT EXISTS "email_accounts_student_id_idx" ON "email_accounts"("studentId");
CREATE INDEX IF NOT EXISTS "email_accounts_department_idx" ON "email_accounts"("department");

-- Payment system indexes
CREATE INDEX IF NOT EXISTS "payments_student_id_idx" ON "payments"("studentId");
CREATE INDEX IF NOT EXISTS "payments_status_idx" ON "payments"("status");
CREATE INDEX IF NOT EXISTS "payments_gateway_idx" ON "payments"("gateway");
CREATE INDEX IF NOT EXISTS "payments_created_at_idx" ON "payments"("createdAt" DESC);
CREATE INDEX IF NOT EXISTS "payments_completed_at_idx" ON "payments"("completedAt" DESC);
CREATE INDEX IF NOT EXISTS "payments_fee_type_idx" ON "payments"("feeType");

-- Composite indexes for payment queries
CREATE INDEX IF NOT EXISTS "payments_student_status_idx" ON "payments"("studentId", "status");
CREATE INDEX IF NOT EXISTS "payments_gateway_status_idx" ON "payments"("gateway", "status");

-- Performance optimization: Partial indexes for common filtered queries
CREATE INDEX IF NOT EXISTS "emails_unread_idx" ON "emails"("createdAt" DESC) WHERE "isRead" = false;
CREATE INDEX IF NOT EXISTS "emails_starred_idx" ON "emails"("createdAt" DESC) WHERE "isStarred" = true;
CREATE INDEX IF NOT EXISTS "emails_not_deleted_idx" ON "emails"("createdAt" DESC) WHERE "isDeleted" = false;
CREATE INDEX IF NOT EXISTS "emails_not_spam_idx" ON "emails"("createdAt" DESC) WHERE "isSpam" = false;

-- Queue processing optimization
CREATE INDEX IF NOT EXISTS "email_queue_pending_idx" ON "email_queue"("scheduledAt") WHERE "status" = 'PENDING';
CREATE INDEX IF NOT EXISTS "email_queue_failed_retry_idx" ON "email_queue"("nextRetry") WHERE "status" = 'PENDING' AND "attempts" > 0;

-- Payment processing optimization
CREATE INDEX IF NOT EXISTS "payments_pending_idx" ON "payments"("createdAt" DESC) WHERE "status" = 'PENDING';
CREATE INDEX IF NOT EXISTS "payments_success_idx" ON "payments"("paidAt" DESC) WHERE "status" = 'SUCCESS';
