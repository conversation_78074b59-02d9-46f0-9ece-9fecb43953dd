# Phase 1: Foundation & Database Migration

## 🎯 **Immediate Action Items**

### **Step 1: Supabase Setup and Migration**

#### **1.1 Create Supabase Project**
```bash
# 1. Go to https://supabase.com
# 2. Create new project (free tier)
# 3. Note down the following credentials:
#    - Project URL
#    - Anon Key
#    - Service Role Key
#    - Database Password
```

#### **1.2 Update Environment Variables**
```env
# Add to .env.local and .env.production
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# Email system configuration
EMAIL_DOMAIN="institute.edu"
EMAIL_ADMIN="<EMAIL>"
EMAIL_DEFAULT_QUOTA="**********"  # 1GB in bytes

# Payment gateway configuration (will be configured later)
PAYU_MERCHANT_KEY=""
PAYU_MERCHANT_SALT=""
PHONEPE_MERCHANT_ID=""
PHONEPE_SALT_KEY=""
CASHFREE_APP_ID=""
CASHFREE_SECRET_KEY=""
```

#### **1.3 Update Prisma Schema**
```prisma
// Update prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"  // Changed from sqlite
  url      = env("DATABASE_URL")
}

// Keep all existing models and add new email system models
// (Copy from EMAIL_SYSTEM_SCHEMA.md)
```

#### **1.4 Install Additional Dependencies**
```bash
npm install @supabase/supabase-js
npm install @supabase/storage-js
npm install bcryptjs
npm install nodemailer
npm install jspdf
npm install html2canvas
npm install crypto-js
npm install uuid
npm install date-fns
npm install zod
```

### **Step 2: Database Migration Script**

#### **2.1 Create Migration Script**
```typescript
// scripts/migrate-to-supabase.ts
import { PrismaClient as SQLitePrisma } from '@prisma/client'
import { PrismaClient as PostgresPrisma } from '@prisma/client'

const sqlitePrisma = new SQLitePrisma({
  datasources: {
    db: {
      url: 'file:./dev.db'
    }
  }
})

const postgresPrisma = new PostgresPrisma()

async function migrateData() {
  console.log('🚀 Starting data migration from SQLite to Supabase...')
  
  try {
    // Migrate Users
    const users = await sqlitePrisma.user.findMany()
    for (const user of users) {
      await postgresPrisma.user.create({
        data: {
          id: user.id,
          email: user.email,
          name: user.name,
          password: user.password,
          role: user.role,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${users.length} users`)

    // Migrate Pages
    const pages = await sqlitePrisma.page.findMany()
    for (const page of pages) {
      await postgresPrisma.page.create({
        data: {
          id: page.id,
          title: page.title,
          slug: page.slug,
          description: page.description,
          content: page.content,
          metaTitle: page.metaTitle,
          metaDesc: page.metaDesc,
          status: page.status,
          parentId: page.parentId,
          order: page.order,
          navigationCategory: page.navigationCategory,
          navigationOrder: page.navigationOrder,
          createdAt: page.createdAt,
          updatedAt: page.updatedAt,
          createdById: page.createdById
        }
      })
    }
    console.log(`✅ Migrated ${pages.length} pages`)

    // Continue for other models...
    
    console.log('🎉 Migration completed successfully!')
  } catch (error) {
    console.error('❌ Migration failed:', error)
  } finally {
    await sqlitePrisma.$disconnect()
    await postgresPrisma.$disconnect()
  }
}

migrateData()
```

### **Step 3: Supabase Storage Setup**

#### **3.1 Create Storage Buckets**
```sql
-- Run in Supabase SQL Editor
-- Create bucket for email attachments
INSERT INTO storage.buckets (id, name, public) VALUES ('email-attachments', 'email-attachments', false);

-- Create bucket for payment receipts
INSERT INTO storage.buckets (id, name, public) VALUES ('payment-receipts', 'payment-receipts', false);

-- Create bucket for general uploads
INSERT INTO storage.buckets (id, name, public) VALUES ('uploads', 'uploads', true);

-- Set up RLS policies for email attachments
CREATE POLICY "Users can upload email attachments" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'email-attachments');

CREATE POLICY "Users can view their email attachments" ON storage.objects
FOR SELECT USING (bucket_id = 'email-attachments');

-- Set up RLS policies for payment receipts
CREATE POLICY "Users can upload payment receipts" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'payment-receipts');

CREATE POLICY "Users can view their payment receipts" ON storage.objects
FOR SELECT USING (bucket_id = 'payment-receipts');
```

#### **3.2 Create Supabase Client**
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.SUPABASE_URL!
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Client for browser/frontend
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Admin client for server-side operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Storage helpers
export const uploadFile = async (bucket: string, path: string, file: File) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file)
  
  if (error) throw error
  return data
}

export const getFileUrl = (bucket: string, path: string) => {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)
  
  return data.publicUrl
}

export const deleteFile = async (bucket: string, path: string) => {
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path])
  
  if (error) throw error
}
```

### **Step 4: Update Prisma Configuration**

#### **4.1 Update package.json Scripts**
```json
{
  "scripts": {
    "db:migrate": "prisma migrate dev",
    "db:deploy": "prisma migrate deploy",
    "db:generate": "prisma generate",
    "db:studio": "prisma studio",
    "db:seed": "tsx prisma/seed.ts",
    "db:reset": "prisma migrate reset",
    "migrate:supabase": "tsx scripts/migrate-to-supabase.ts",
    "vercel-build": "prisma generate && prisma migrate deploy && next build"
  }
}
```

#### **4.2 Update Prisma Client Configuration**
```typescript
// lib/prisma.ts - Update existing file
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

function createPrismaClient() {
  const isBuildTime = (
    (process.env.VERCEL === '1' && !process.env.VERCEL_URL) ||
    process.env.NEXT_PHASE === 'phase-production-build' ||
    (process.env.NODE_ENV === 'production' && !global.fetch)
  )

  if (isBuildTime) {
    console.warn('Skipping Prisma Client creation during build time')
    return null as any
  }

  try {
    return new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
      errorFormat: 'pretty',
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    })
  } catch (error) {
    console.error('Failed to create Prisma Client:', error)
    return null as any
  }
}

export const prisma = globalForPrisma.prisma ?? createPrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

### **Step 5: Authentication System Extension**

#### **5.1 Create Student Authentication Provider**
```typescript
// lib/student-auth.ts
import { NextAuthOptions } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./prisma"

export const studentAuthOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "student-credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          const emailAccount = await prisma.emailAccount.findUnique({
            where: {
              email: credentials.email,
              isActive: true
            }
          })

          if (!emailAccount) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            emailAccount.password
          )

          if (!isPasswordValid) {
            return null
          }

          // Update last login
          await prisma.emailAccount.update({
            where: { id: emailAccount.id },
            data: { 
              lastLogin: new Date(),
              loginAttempts: 0
            }
          })

          return {
            id: emailAccount.id,
            email: emailAccount.email,
            name: emailAccount.displayName || `${emailAccount.firstName} ${emailAccount.lastName}`,
            type: emailAccount.type,
            studentId: emailAccount.studentId,
            department: emailAccount.department
          }
        } catch (error) {
          console.error("Student authentication error:", error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt" as const,
    maxAge: 24 * 60 * 60, // 24 hours for students
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.type = user.type
        token.studentId = user.studentId
        token.department = user.department
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id || token.sub!
        session.user.type = token.type as string
        session.user.studentId = token.studentId as string
        session.user.department = token.department as string
      }
      return session
    }
  },
  pages: {
    signIn: "/student/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
}
```

## 🚀 **Execution Checklist for Phase 1**

- [ ] Create Supabase project and note credentials
- [ ] Update environment variables
- [ ] Install additional dependencies
- [ ] Update Prisma schema with email system models
- [ ] Create and run database migration
- [ ] Set up Supabase storage buckets
- [ ] Create Supabase client utilities
- [ ] Update Prisma client configuration
- [ ] Implement student authentication system
- [ ] Test database connection and basic operations
- [ ] Verify Supabase storage functionality
- [ ] Create initial email server configuration

## 📝 **Next Steps**
After completing Phase 1, proceed to Phase 2: Email Core System implementation.
