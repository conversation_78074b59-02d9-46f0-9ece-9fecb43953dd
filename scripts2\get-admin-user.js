const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function getAdminUser() {
  try {
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'ADMIN'
      }
    })
    
    if (adminUser) {
      console.log('Admin User ID:', adminUser.id)
      console.log('Admin User Email:', adminUser.email)
    } else {
      console.log('No admin user found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

getAdminUser()
