const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Bulk page creation for all remaining pages
const bulkPages = [
  // Trainee Section (remaining)
  {
    title: 'Attendance of Trainees',
    slug: 'attendance-of-trainee',
    description: 'Trainee attendance tracking system and attendance records management.',
    content: `<h2>Attendance of Trainees</h2><p>S.N. Pvt. Industrial Training Institute maintains strict attendance records for all trainees as per NCVT guidelines.</p><h3>Attendance Policy:</h3><ul><li>Minimum 75% attendance required for examination eligibility</li><li>Daily attendance marking in theory and practical classes</li><li>Monthly attendance reports to parents/guardians</li><li>Regular monitoring and counseling for low attendance</li></ul><h3>Attendance System:</h3><ul><li>Digital attendance management system</li><li>Biometric attendance for accuracy</li><li>Real-time attendance tracking</li><li>Automated reports generation</li></ul>`,
    metaTitle: 'Trainee Attendance - S.N. Pvt. ITI',
    metaDesc: 'Trainee attendance tracking system and attendance management at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Certificates Issued To Trainees',
    slug: 'certificate-issued',
    description: 'Information about certificates issued to trainees upon successful completion of courses.',
    content: `<h2>Certificates Issued To Trainees</h2><p>S.N. Pvt. Industrial Training Institute issues NCVT certificates to successful trainees.</p><h3>Certificate Types:</h3><ul><li>NCVT Certificate for Electrician Trade</li><li>Course completion certificates</li><li>Skill development certificates</li><li>Participation certificates</li></ul><h3>Certificate Process:</h3><ul><li>Successful completion of 2-year course</li><li>Passing NCVT examinations</li><li>Meeting attendance requirements</li><li>Completion of practical training</li></ul>`,
    metaTitle: 'Certificates Issued - S.N. Pvt. ITI',
    metaDesc: 'Information about NCVT and other certificates issued to trainees at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Progress Card',
    slug: 'progress-card',
    description: 'Student progress tracking and evaluation system for continuous assessment.',
    content: `<h2>Progress Card</h2><p>Regular progress evaluation and assessment system for trainee development tracking.</p><h3>Progress Evaluation:</h3><ul><li>Monthly progress assessments</li><li>Theory and practical evaluations</li><li>Skill development tracking</li><li>Continuous internal assessment</li></ul><h3>Progress Components:</h3><ul><li>Academic performance</li><li>Practical skills</li><li>Attendance record</li><li>Behavior and discipline</li></ul>`,
    metaTitle: 'Progress Card - S.N. Pvt. ITI',
    metaDesc: 'Student progress tracking and evaluation system at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Energy Consumption',
    slug: 'ee-consumption-pspm',
    description: 'Energy consumption monitoring and management per student per month.',
    content: `<h2>Energy Consumption</h2><p>Energy consumption monitoring and management system at the institute.</p><h3>Energy Management:</h3><ul><li>Monthly energy consumption tracking</li><li>Per student energy usage calculation</li><li>Energy conservation measures</li><li>Efficient equipment usage</li></ul><h3>Conservation Efforts:</h3><ul><li>LED lighting systems</li><li>Energy-efficient equipment</li><li>Solar power initiatives</li><li>Awareness programs</li></ul>`,
    metaTitle: 'Energy Consumption - S.N. Pvt. ITI',
    metaDesc: 'Energy consumption monitoring and conservation efforts at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Raw Material Consumption',
    slug: 'rm-consumption-pspm',
    description: 'Raw material consumption tracking and management per student per month.',
    content: `<h2>Raw Material Consumption</h2><p>Raw material consumption tracking for training activities and workshops.</p><h3>Material Management:</h3><ul><li>Monthly raw material consumption tracking</li><li>Per student material usage calculation</li><li>Inventory management system</li><li>Cost-effective procurement</li></ul><h3>Materials Used:</h3><ul><li>Electrical components and wires</li><li>Tools and equipment</li><li>Safety materials</li><li>Consumable items</li></ul>`,
    metaTitle: 'Raw Material Consumption - S.N. Pvt. ITI',
    metaDesc: 'Raw material consumption tracking and management at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  // Staff Section
  {
    title: 'Administrative Staff',
    slug: 'administrative-staff',
    description: 'Information about administrative staff members and their roles at the institute.',
    content: `<h2>Administrative Staff</h2><p>Dedicated administrative staff ensuring smooth operations of the institute.</p><h3>Administrative Team:</h3><ul><li>Principal/Director</li><li>Office Superintendent</li><li>Accounts Officer</li><li>Office Assistant</li><li>Support Staff</li></ul><h3>Responsibilities:</h3><ul><li>Student admission and records</li><li>Financial management</li><li>Administrative coordination</li><li>Government liaison</li><li>General administration</li></ul>`,
    metaTitle: 'Administrative Staff - S.N. Pvt. ITI',
    metaDesc: 'Information about administrative staff members and their roles at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Attendance of Instructor',
    slug: 'attendance-instructor',
    description: 'Instructor attendance tracking and management system.',
    content: `<h2>Attendance of Instructor</h2><p>Regular attendance tracking for all instructors and teaching staff.</p><h3>Instructor Attendance:</h3><ul><li>Daily attendance marking</li><li>Monthly attendance reports</li><li>Leave management system</li><li>Substitute arrangements</li></ul><h3>Attendance Policy:</h3><ul><li>Regular working hours compliance</li><li>Professional commitment</li><li>Student welfare priority</li><li>Quality education delivery</li></ul>`,
    metaTitle: 'Instructor Attendance - S.N. Pvt. ITI',
    metaDesc: 'Instructor attendance tracking and management system at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  // More Section (Industry Linkage)
  {
    title: 'Industry Institute Linkage',
    slug: 'industry-linkages',
    description: 'Industry-institute collaboration and linkage programs for practical exposure.',
    content: `<h2>Industry Institute Linkage</h2><p>Strong industry partnerships for practical training and employment opportunities.</p><h3>Industry Partnerships:</h3><ul><li>Local electrical companies</li><li>Manufacturing industries</li><li>Service sector organizations</li><li>Government departments</li></ul><h3>Collaboration Activities:</h3><ul><li>Industrial visits and tours</li><li>Guest lectures by industry experts</li><li>Practical training programs</li><li>Placement assistance</li></ul>`,
    metaTitle: 'Industry Linkage - S.N. Pvt. ITI',
    metaDesc: 'Industry-institute collaboration and linkage programs at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Name of the Industry Partner',
    slug: 'industry-partner',
    description: 'List of industry partners collaborating with the institute for training and placement.',
    content: `<h2>Name of the Industry Partner</h2><p>Industry partners supporting training and placement activities.</p><h3>Partner Organizations:</h3><ul><li>Local electrical contractors</li><li>Power distribution companies</li><li>Manufacturing units</li><li>Service providers</li></ul><h3>Partnership Benefits:</h3><ul><li>Practical training opportunities</li><li>Industry exposure for students</li><li>Employment opportunities</li><li>Skill development programs</li></ul>`,
    metaTitle: 'Industry Partners - S.N. Pvt. ITI',
    metaDesc: 'List of industry partners collaborating with S.N. Pvt. ITI for training and placement opportunities.',
    status: 'PUBLISHED'
  }
]

async function createBulkPages() {
  try {
    console.log('🚀 Starting bulk page creation...')
    
    for (const pageData of bulkPages) {
      console.log(`Creating page: ${pageData.title}`)
      
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })
      
      if (existingPage) {
        console.log(`⚠️  Page ${pageData.slug} already exists, skipping...`)
        continue
      }
      
      // Create the page
      await prisma.page.create({
        data: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          content: pageData.content,
          metaTitle: pageData.metaTitle,
          metaDesc: pageData.metaDesc,
          status: pageData.status,
          order: 0,
          createdById: 'cmc1p577i0000fcy4iafh42k2' // <EMAIL>
        }
      })
      
      console.log(`✅ Created: ${pageData.title}`)
    }
    
    console.log('🎉 Bulk page creation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error creating pages:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createBulkPages()
