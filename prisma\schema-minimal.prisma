// Minimal Cloud-Ready Schema for SNPITC System
// Compatible with SQLite (dev) and PostgreSQL (production)

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User management for admin panel
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdEmailAccounts EmailAccount[]
  createdPaymentItems PaymentCatalogItem[]

  @@map("users")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Email system models
model EmailAccount {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String
  type              EmailAccountType @default(STUDENT)
  isActive          Boolean   @default(true)
  quota             Int       @default(**********) // 1GB in bytes
  usedQuota         Int       @default(0)
  firstName         String?
  lastName          String?
  displayName       String?
  department        String?
  studentId         String?
  signature         String?
  autoReply         Boolean   @default(false)
  autoReplyMessage  String?
  forwardingEmail   String?
  lastLogin         DateTime?
  loginAttempts     Int       @default(0)
  lockedUntil       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       String?
  createdBy         User?     @relation(fields: [createdById], references: [id])

  // Relations
  sentEmails     Email[]       @relation("SentEmails")
  receivedEmails Email[]       @relation("ReceivedEmails")
  folders        EmailFolder[]
  payments       Payment[]

  @@map("email_accounts")
}

enum EmailAccountType {
  STUDENT
  ADMIN
  STAFF
}

model Email {
  id            String   @id @default(cuid())
  messageId     String   @unique
  subject       String
  body          String
  bodyText      String?
  fromEmail     String
  fromName      String?
  priority      EmailPriority @default(NORMAL)
  isRead        Boolean  @default(false)
  isStarred     Boolean  @default(false)
  isDraft       Boolean  @default(false)
  sentAt        DateTime?
  deliveredAt   DateTime?
  readAt        DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  fromAccountId String?
  fromAccount   EmailAccount? @relation("SentEmails", fields: [fromAccountId], references: [id])
  toAccountId   String?
  toAccount     EmailAccount? @relation("ReceivedEmails", fields: [toAccountId], references: [id])

  // Relations
  recipients  EmailRecipient[]
  attachments EmailAttachment[]
  folders     EmailFolder[]    @relation("EmailFolders")

  @@map("emails")
}

enum EmailPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

model EmailRecipient {
  id      String            @id @default(cuid())
  email   String
  name    String?
  type    EmailRecipientType
  emailId String
  email_rel Email           @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_recipients")
}

enum EmailRecipientType {
  TO
  CC
  BCC
}

model EmailAttachment {
  id       String @id @default(cuid())
  filename String
  size     Int
  mimeType String
  path     String
  emailId  String
  email    Email  @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_attachments")
}

model EmailFolder {
  id          String      @id @default(cuid())
  name        String
  type        FolderType  @default(CUSTOM)
  isSystem    Boolean     @default(false)
  emailCount  Int         @default(0)
  unreadCount Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  accountId   String
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)

  // Relations
  emails Email[] @relation("EmailFolders")

  @@map("email_folders")
}

enum FolderType {
  INBOX
  SENT
  DRAFTS
  TRASH
  SPAM
  CUSTOM
}

// Payment system models
model PaymentCatalogItem {
  id                  String   @id @default(cuid())
  name                String
  description         String?
  feeType             FeeType
  amount              Float
  currency            String   @default("INR")
  category            String?
  subcategory         String?
  isActive            Boolean  @default(true)
  isRecurring         Boolean  @default(false)
  recurringInterval   String?
  lateFee             Float?
  discountPercentage  Float?
  dueDate             DateTime?
  applicableFor       String   @default("STUDENT")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  deletedAt           DateTime?
  createdById         String?
  createdBy           User?    @relation(fields: [createdById], references: [id])

  // Relations
  payments Payment[]

  @@map("payment_catalog_items")
}

enum FeeType {
  ADMISSION
  SEMESTER
  EXAM
  LIBRARY
  HOSTEL
  TRANSPORT
  OTHER
}

model Payment {
  id              String        @id @default(cuid())
  transactionId   String        @unique
  studentId       String
  student         EmailAccount  @relation(fields: [studentId], references: [id])
  catalogItemId   String
  catalogItem     PaymentCatalogItem @relation(fields: [catalogItemId], references: [id])
  amount          Float
  currency        String        @default("INR")
  feeType         String?
  description     String?
  gateway         String?
  gatewayTxnId    String?
  gatewayResponse String?       // JSON as string for SQLite compatibility
  additionalFee   Float?
  totalAmount     Float
  status          PaymentStatus @default(PENDING)
  failureReason   String?
  receiptNumber   String?
  receiptUrl      String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  completedAt     DateTime?

  @@map("payments")
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  CANCELLED
}

model PaymentGatewayConfig {
  id          String   @id @default(cuid())
  gateway     String   @unique
  isEnabled   Boolean  @default(false)
  isTestMode  Boolean  @default(true)
  config      String   // JSON as string for SQLite compatibility
  feeType     String?
  feeAmount   Float?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("payment_gateway_configs")
}

// System models
model Settings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("STRING")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
