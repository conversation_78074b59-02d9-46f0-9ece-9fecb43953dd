const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function resetAdminPassword() {
  try {
    console.log('🔄 Resetting admin password...')

    // Hash the new password
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    // Update the admin user
    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    })
    
    console.log('✅ Admin password reset successfully for:', updatedUser.email)
    
    // Test the new password
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (testUser) {
      const isPasswordValid = await bcrypt.compare('admin123', testUser.password)
      console.log('✅ Password test:', isPasswordValid ? 'VALID' : 'INVALID')
    }
    
    console.log('\n🔑 Admin credentials:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')

  } catch (error) {
    console.error('❌ Failed to reset admin password:', error)
  } finally {
    await prisma.$disconnect()
  }
}

resetAdminPassword()
