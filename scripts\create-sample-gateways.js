const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSampleGateways() {
  try {
    console.log('Creating sample payment gateway configurations...')

    // Check if gateways already exist
    const existingGateways = await prisma.paymentGatewayConfig.findMany()
    
    if (existingGateways.length > 0) {
      console.log('Payment gateways already exist. Skipping creation.')
      return
    }

    const sampleGateways = [
      {
        gateway: 'RAZORPAY',
        isEnabled: true,
        isTestMode: true,
        apiKey: 'rzp_test_1234567890abcdef',
        secretKey: 'test_secret_key_razorpay_123456',
        webhookUrl: 'https://institute.edu/api/webhooks/razorpay'
      },
      {
        gateway: 'STRIPE',
        isEnabled: false,
        isTestMode: true,
        apiKey: 'pk_test_1234567890abcdef',
        secretKey: 'sk_test_secret_key_stripe_123456',
        webhookUrl: 'https://institute.edu/api/webhooks/stripe'
      },
      {
        gateway: 'PAYU',
        isEnabled: true,
        isTestMode: true,
        apiKey: 'payu_test_key_123456',
        secretKey: 'payu_test_secret_123456',
        webhookUrl: 'https://institute.edu/api/webhooks/payu'
      },
      {
        gateway: 'CASHFREE',
        isEnabled: false,
        isTestMode: true,
        apiKey: 'cf_test_key_123456',
        secretKey: 'cf_test_secret_123456',
        webhookUrl: 'https://institute.edu/api/webhooks/cashfree'
      },
      {
        gateway: 'PHONEPE',
        isEnabled: false,
        isTestMode: true,
        apiKey: 'phonepe_test_key_123456',
        secretKey: 'phonepe_test_secret_123456',
        webhookUrl: 'https://institute.edu/api/webhooks/phonepe'
      }
    ]

    // Create gateway configurations
    for (const gateway of sampleGateways) {
      await prisma.paymentGatewayConfig.create({
        data: gateway
      })
    }

    console.log(`✅ Created ${sampleGateways.length} payment gateway configurations`)
    
    // Display summary
    const summary = await prisma.paymentGatewayConfig.groupBy({
      by: ['isEnabled'],
      _count: {
        isEnabled: true
      }
    })

    console.log('\n📊 Gateway Summary:')
    summary.forEach(item => {
      console.log(`${item.isEnabled ? 'Enabled' : 'Disabled'}: ${item._count.isEnabled} gateways`)
    })

  } catch (error) {
    console.error('Error creating sample gateways:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSampleGateways()
