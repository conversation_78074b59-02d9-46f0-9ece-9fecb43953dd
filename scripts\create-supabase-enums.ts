import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function createEnums() {
  try {
    console.log('🔧 Creating PostgreSQL enums for Supabase...')

    // Create UserRole enum
    console.log('👤 Creating UserRole enum...')
    await prisma.$executeRawUnsafe(`
      DO $$ BEGIN
        CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'EDITOR', 'VIEWER');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `)

    // Create AccountType enum
    console.log('📧 Creating AccountType enum...')
    await prisma.$executeRawUnsafe(`
      DO $$ BEGIN
        CREATE TYPE "AccountType" AS ENUM ('STUDENT', 'ADMIN', 'STAFF');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `)

    // Create FeeType enum
    console.log('💰 Creating FeeType enum...')
    await prisma.$executeRawUnsafe(`
      DO $$ BEGIN
        CREATE TYPE "FeeType" AS ENUM ('ADMISSION', 'SEMESTER', 'EXAM', 'LIBRARY', 'HOSTEL', 'TRANSPORT', 'OTHER');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `)

    // Create PaymentStatus enum
    console.log('💳 Creating PaymentStatus enum...')
    await prisma.$executeRawUnsafe(`
      DO $$ BEGIN
        CREATE TYPE "PaymentStatus" AS ENUM ('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `)

    // Create PaymentGateway enum
    console.log('🏦 Creating PaymentGateway enum...')
    await prisma.$executeRawUnsafe(`
      DO $$ BEGIN
        CREATE TYPE "PaymentGateway" AS ENUM ('RAZORPAY', 'STRIPE', 'PAYU', 'PHONEPE', 'CASHFREE');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `)

    console.log('✅ All enums created successfully!')

    // Now update the users table to use the enum
    console.log('🔄 Updating users table to use UserRole enum...')
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "users" 
      ALTER COLUMN "role" TYPE "UserRole" 
      USING "role"::"UserRole";
    `)

    // Update email_accounts table to use AccountType enum
    console.log('🔄 Updating email_accounts table to use AccountType enum...')
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "email_accounts" 
      ALTER COLUMN "type" TYPE "AccountType" 
      USING "type"::"AccountType";
    `)

    // Update payment_catalog_items table to use FeeType enum
    console.log('🔄 Updating payment_catalog_items table to use FeeType enum...')
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "payment_catalog_items" 
      ALTER COLUMN "feeType" TYPE "FeeType" 
      USING "feeType"::"FeeType";
    `)

    // Update payments table to use enums
    console.log('🔄 Updating payments table to use enums...')
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "payments" 
      ALTER COLUMN "feeType" TYPE "FeeType" 
      USING "feeType"::"FeeType";
    `)

    await prisma.$executeRawUnsafe(`
      ALTER TABLE "payments" 
      ALTER COLUMN "gateway" TYPE "PaymentGateway" 
      USING "gateway"::"PaymentGateway";
    `)

    await prisma.$executeRawUnsafe(`
      ALTER TABLE "payments" 
      ALTER COLUMN "status" TYPE "PaymentStatus" 
      USING "status"::"PaymentStatus";
    `)

    console.log('✅ All table columns updated to use enums!')

  } catch (error) {
    console.error('❌ Error creating enums:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

createEnums()
  .then(() => {
    console.log('🎉 Supabase enums setup completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Enum creation failed:', error.message)
    process.exit(1)
  })
