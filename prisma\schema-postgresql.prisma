// PostgreSQL Schema for SNPITC System
// This schema is optimized for Supabase/PostgreSQL deployment

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management for admin panel
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(EDITOR)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdPages    Page[]
  createdMedia    Media[]
  createdContents Content[]
  createdEmailAccounts EmailAccount[]
  sentEmails      Email[]    @relation("SentEmails")
  createdPaymentItems PaymentCatalogItem[] @relation("CreatedPaymentItems")
  adminActions        AdminAuditLog[]     @relation("AdminActions")
  acknowledgedAlerts  SystemAlert[]       @relation("AcknowledgedAlerts")

  @@map("users")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Page management
model Page {
  id          Int         @id @default(autoincrement())
  title       String
  slug        String      @unique
  description String?
  content     String?
  metaTitle   String?
  metaDesc    String?
  isPublished Boolean     @default(false)
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById Int
  createdBy   User        @relation(fields: [createdById], references: [id])

  // Relations
  contents Content[]

  @@map("pages")
}

// Content blocks for pages
model Content {
  id        Int         @id @default(autoincrement())
  type      ContentType
  title     String?
  content   String?
  order     Int         @default(0)
  isVisible Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  pageId    Int
  page      Page        @relation(fields: [pageId], references: [id], onDelete: Cascade)
  createdById Int
  createdBy   User      @relation(fields: [createdById], references: [id])

  @@map("contents")
}

enum ContentType {
  HERO
  TEXT
  IMAGE
  GALLERY
  CONTACT
  FEATURES
  TESTIMONIALS
  FAQ
}

// Media management
model Media {
  id          Int      @id @default(autoincrement())
  filename    String
  originalName String
  mimeType    String
  size        Int
  path        String
  url         String?
  alt         String?
  caption     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById Int
  createdBy   User     @relation(fields: [createdById], references: [id])

  @@map("media")
}

// Email system models
model EmailAccount {
  id                Int       @id @default(autoincrement())
  email             String    @unique
  password          String
  type              EmailAccountType @default(STUDENT)
  isActive          Boolean   @default(true)
  quota             BigInt    @default(**********) // 1GB in bytes
  usedQuota         BigInt    @default(0)
  firstName         String?
  lastName          String?
  displayName       String?
  department        String?
  studentId         String?
  signature         String?
  autoReply         Boolean   @default(false)
  autoReplyMessage  String?
  forwardingEmail   String?
  lastLogin         DateTime?
  loginAttempts     Int       @default(0)
  lockedUntil       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       Int
  createdBy         User      @relation(fields: [createdById], references: [id])

  // Relations
  sentEmails     Email[]       @relation("SentEmails")
  receivedEmails Email[]       @relation("ReceivedEmails")
  folders        EmailFolder[]
  payments       Payment[]

  @@map("email_accounts")
}

enum EmailAccountType {
  STUDENT
  ADMIN
  STAFF
}

model Email {
  id            Int      @id @default(autoincrement())
  messageId     String   @unique
  subject       String
  body          String
  bodyText      String?
  fromEmail     String
  fromName      String?
  priority      EmailPriority @default(NORMAL)
  isRead        Boolean  @default(false)
  isStarred     Boolean  @default(false)
  isDraft       Boolean  @default(false)
  sentAt        DateTime?
  deliveredAt   DateTime?
  readAt        DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  fromAccountId Int?
  fromAccount   EmailAccount? @relation("SentEmails", fields: [fromAccountId], references: [id])
  toAccountId   Int?
  toAccount     EmailAccount? @relation("ReceivedEmails", fields: [toAccountId], references: [id])
  sentById      Int?
  sentBy        User?    @relation("SentEmails", fields: [sentById], references: [id])

  // Relations
  recipients  EmailRecipient[]
  attachments EmailAttachment[]
  folders     EmailFolder[]    @relation("EmailFolders")

  @@map("emails")
}

enum EmailPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

model EmailRecipient {
  id      Int               @id @default(autoincrement())
  email   String
  name    String?
  type    EmailRecipientType
  emailId Int
  email_rel Email           @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_recipients")
}

enum EmailRecipientType {
  TO
  CC
  BCC
}

model EmailAttachment {
  id       Int    @id @default(autoincrement())
  filename String
  size     Int
  mimeType String
  path     String
  emailId  Int
  email    Email  @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@map("email_attachments")
}

model EmailFolder {
  id          Int         @id @default(autoincrement())
  name        String
  type        FolderType  @default(CUSTOM)
  isSystem    Boolean     @default(false)
  emailCount  Int         @default(0)
  unreadCount Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  accountId   Int
  account     EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)

  // Relations
  emails Email[] @relation("EmailFolders")

  @@map("email_folders")
}

enum FolderType {
  INBOX
  SENT
  DRAFTS
  TRASH
  SPAM
  CUSTOM
}

// Payment system models
model PaymentCatalogItem {
  id                  Int      @id @default(autoincrement())
  name                String
  description         String?
  feeType             FeeType
  amount              Decimal  @db.Decimal(10, 2)
  currency            String   @default("INR")
  category            String?
  subcategory         String?
  isActive            Boolean  @default(true)
  isRecurring         Boolean  @default(false)
  recurringInterval   String?
  lateFee             Decimal? @db.Decimal(10, 2)
  discountPercentage  Decimal? @db.Decimal(5, 2)
  dueDate             DateTime?
  applicableFor       String   @default("STUDENT")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  deletedAt           DateTime?
  createdById         Int
  createdBy           User     @relation("CreatedPaymentItems", fields: [createdById], references: [id])

  // Relations
  payments Payment[]

  @@map("payment_catalog_items")
}

enum FeeType {
  ADMISSION
  SEMESTER
  EXAM
  LIBRARY
  HOSTEL
  TRANSPORT
  OTHER
}

model Payment {
  id              Int              @id @default(autoincrement())
  transactionId   String           @unique
  studentId       Int
  student         EmailAccount     @relation(fields: [studentId], references: [id])
  catalogItemId   Int
  catalogItem     PaymentCatalogItem @relation(fields: [catalogItemId], references: [id])
  amount          Decimal          @db.Decimal(10, 2)
  currency        String           @default("INR")
  feeType         String?
  description     String?
  gateway         String?
  gatewayTxnId    String?
  gatewayResponse Json?
  additionalFee   Decimal?         @db.Decimal(10, 2)
  totalAmount     Decimal          @db.Decimal(10, 2)
  status          PaymentStatus    @default(PENDING)
  failureReason   String?
  receiptNumber   String?
  receiptUrl      String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  completedAt     DateTime?

  @@map("payments")
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  CANCELLED
}

model PaymentGatewayConfig {
  id          Int      @id @default(autoincrement())
  gateway     String   @unique
  isEnabled   Boolean  @default(false)
  isTestMode  Boolean  @default(true)
  config      Json
  feeType     String?
  feeAmount   Decimal? @db.Decimal(10, 2)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("payment_gateway_configs")
}

// System models
model Settings {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  type      String   @default("STRING")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

model AdminAuditLog {
  id          Int      @id @default(autoincrement())
  action      String
  resource    String
  resourceId  String?
  details     Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  adminId     Int
  admin       User     @relation("AdminActions", fields: [adminId], references: [id])

  @@map("admin_audit_logs")
}

model SystemAlert {
  id              Int       @id @default(autoincrement())
  type            String
  title           String
  message         String
  severity        String    @default("INFO")
  isRead          Boolean   @default(false)
  isAcknowledged  Boolean   @default(false)
  acknowledgedAt  DateTime?
  createdAt       DateTime  @default(now())
  acknowledgedById Int?
  acknowledgedBy   User?    @relation("AcknowledgedAlerts", fields: [acknowledgedById], references: [id])

  @@map("system_alerts")
}
