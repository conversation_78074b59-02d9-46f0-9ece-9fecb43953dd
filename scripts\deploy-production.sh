#!/bin/bash

# Production Deployment Script for SNPITC Email & Payment System
# This script handles the complete deployment process including:
# - Environment validation
# - Database migrations
# - Application deployment
# - Health checks
# - Rollback capabilities

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_LOG="/var/log/snpitc-deployment.log"
BACKUP_DIR="/backups/pre-deployment"
MAX_ROLLBACK_ATTEMPTS=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$DEPLOYMENT_LOG"
}

log_info() { log "INFO" "${BLUE}$@${NC}"; }
log_success() { log "SUCCESS" "${GREEN}$@${NC}"; }
log_warning() { log "WARNING" "${YELLOW}$@${NC}"; }
log_error() { log "ERROR" "${RED}$@${NC}"; }

# Error handling
handle_error() {
    local exit_code=$?
    local line_number=$1
    log_error "Deployment failed at line $line_number with exit code $exit_code"
    log_error "Check the deployment log for details: $DEPLOYMENT_LOG"
    
    # Offer rollback
    read -p "Would you like to attempt a rollback? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rollback_deployment
    fi
    
    exit $exit_code
}

trap 'handle_error $LINENO' ERR

# Validation functions
validate_environment() {
    log_info "Validating deployment environment..."
    
    # Check required environment variables
    local required_vars=(
        "DATABASE_URL"
        "NEXTAUTH_SECRET"
        "NEXTAUTH_URL"
        "ENCRYPTION_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Check Docker and Docker Compose
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check disk space (minimum 5GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local min_space=5242880  # 5GB in KB
    
    if [[ $available_space -lt $min_space ]]; then
        log_error "Insufficient disk space. Available: ${available_space}KB, Required: ${min_space}KB"
        exit 1
    fi
    
    log_success "Environment validation passed"
}

validate_configuration() {
    log_info "Validating configuration files..."
    
    local config_files=(
        "docker-compose.production.yml"
        "Dockerfile.production"
        "nginx/nginx.conf"
        "monitoring/prometheus.yml"
        ".env.production"
    )
    
    for file in "${config_files[@]}"; do
        if [[ ! -f "$PROJECT_ROOT/$file" ]]; then
            log_error "Required configuration file not found: $file"
            exit 1
        fi
    done
    
    # Validate Docker Compose file
    cd "$PROJECT_ROOT"
    if ! docker-compose -f docker-compose.production.yml config > /dev/null; then
        log_error "Invalid Docker Compose configuration"
        exit 1
    fi
    
    log_success "Configuration validation passed"
}

# Backup functions
create_backup() {
    log_info "Creating pre-deployment backup..."
    
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_path="$BACKUP_DIR/$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup database
    if [[ -n "$DATABASE_URL" ]]; then
        log_info "Backing up database..."
        pg_dump "$DATABASE_URL" > "$backup_path/database.sql"
        log_success "Database backup created: $backup_path/database.sql"
    fi
    
    # Backup application data
    if [[ -d "/app/uploads" ]]; then
        log_info "Backing up application data..."
        tar -czf "$backup_path/app_data.tar.gz" -C /app uploads
        log_success "Application data backup created: $backup_path/app_data.tar.gz"
    fi
    
    # Store current deployment info
    echo "DEPLOYMENT_TIMESTAMP=$backup_timestamp" > "$backup_path/deployment_info"
    echo "GIT_COMMIT=$(git rev-parse HEAD)" >> "$backup_path/deployment_info"
    echo "GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)" >> "$backup_path/deployment_info"
    
    echo "$backup_path"
}

# Database migration
run_migrations() {
    log_info "Running database migrations..."
    
    cd "$PROJECT_ROOT"
    
    # Generate Prisma client
    npx prisma generate
    
    # Run migrations
    npx prisma migrate deploy
    
    # Validate migration
    if ! npx prisma migrate status; then
        log_error "Database migration validation failed"
        exit 1
    fi
    
    log_success "Database migrations completed successfully"
}

# Deployment functions
build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build production image
    docker-compose -f docker-compose.production.yml build --no-cache
    
    # Tag images with timestamp
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    docker tag snpitc-app:latest "snpitc-app:$timestamp"
    
    log_success "Docker images built successfully"
}

deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_ROOT"
    
    # Stop existing services gracefully
    if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        log_info "Stopping existing services..."
        docker-compose -f docker-compose.production.yml down --timeout 30
    fi
    
    # Start new services
    log_info "Starting new services..."
    docker-compose -f docker-compose.production.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    log_success "Services deployed successfully"
}

# Health check functions
run_health_checks() {
    log_info "Running health checks..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts"
        
        # Check application health
        if curl -f -s http://localhost:3000/api/health > /dev/null; then
            log_success "Application health check passed"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Health checks failed after $max_attempts attempts"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # Run comprehensive health checks
    log_info "Running comprehensive health checks..."
    
    local health_checks=(
        "http://localhost:3000/api/health"
        "http://localhost:3000/api/admin/health"
        "http://localhost:9090/-/healthy"  # Prometheus
        "http://localhost:3001/api/health"  # Grafana
    )
    
    for endpoint in "${health_checks[@]}"; do
        if curl -f -s "$endpoint" > /dev/null; then
            log_success "Health check passed: $endpoint"
        else
            log_warning "Health check failed: $endpoint"
        fi
    done
    
    log_success "Health checks completed"
}

# Rollback function
rollback_deployment() {
    log_warning "Starting deployment rollback..."
    
    local latest_backup=$(ls -t "$BACKUP_DIR" | head -n 1)
    
    if [[ -z "$latest_backup" ]]; then
        log_error "No backup found for rollback"
        return 1
    fi
    
    local backup_path="$BACKUP_DIR/$latest_backup"
    log_info "Rolling back to backup: $backup_path"
    
    # Stop current services
    cd "$PROJECT_ROOT"
    docker-compose -f docker-compose.production.yml down
    
    # Restore database
    if [[ -f "$backup_path/database.sql" ]]; then
        log_info "Restoring database..."
        psql "$DATABASE_URL" < "$backup_path/database.sql"
        log_success "Database restored"
    fi
    
    # Restore application data
    if [[ -f "$backup_path/app_data.tar.gz" ]]; then
        log_info "Restoring application data..."
        tar -xzf "$backup_path/app_data.tar.gz" -C /app
        log_success "Application data restored"
    fi
    
    # Restart services with previous version
    # Note: This assumes previous images are still available
    docker-compose -f docker-compose.production.yml up -d
    
    log_success "Rollback completed"
}

# Cleanup function
cleanup_old_resources() {
    log_info "Cleaning up old resources..."
    
    # Remove old Docker images (keep last 3)
    docker images snpitc-app --format "table {{.Tag}}" | grep -E '^[0-9]{8}_[0-9]{6}$' | sort -r | tail -n +4 | xargs -r docker rmi
    
    # Remove old backups (keep last 7 days)
    find "$BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} +
    
    # Clean up Docker system
    docker system prune -f
    
    log_success "Cleanup completed"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "Running post-deployment tasks..."
    
    # Update monitoring dashboards
    if [[ -f "$PROJECT_ROOT/monitoring/update-dashboards.sh" ]]; then
        bash "$PROJECT_ROOT/monitoring/update-dashboards.sh"
    fi
    
    # Send deployment notification
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"SNPITC Email & Payment System deployed successfully"}' \
            "$SLACK_WEBHOOK_URL"
    fi
    
    # Update deployment status
    echo "LAST_DEPLOYMENT=$(date '+%Y-%m-%d %H:%M:%S')" > /var/lib/snpitc/deployment_status
    echo "GIT_COMMIT=$(git rev-parse HEAD)" >> /var/lib/snpitc/deployment_status
    
    log_success "Post-deployment tasks completed"
}

# Main deployment function
main() {
    log_info "Starting SNPITC Email & Payment System deployment..."
    log_info "Deployment log: $DEPLOYMENT_LOG"
    
    # Create necessary directories
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/var/lib/snpitc"
    mkdir -p "/var/log"
    
    # Validation phase
    validate_environment
    validate_configuration
    
    # Backup phase
    local backup_path=$(create_backup)
    log_info "Backup created at: $backup_path"
    
    # Migration phase
    run_migrations
    
    # Build phase
    build_images
    
    # Deployment phase
    deploy_services
    
    # Verification phase
    run_health_checks
    
    # Post-deployment phase
    post_deployment_tasks
    cleanup_old_resources
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at: $NEXTAUTH_URL"
    log_info "Monitoring dashboard: http://localhost:3001"
    log_info "Prometheus metrics: http://localhost:9090"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. Consider using a dedicated deployment user."
    fi
    
    # Load environment variables
    if [[ -f "$PROJECT_ROOT/.env.production" ]]; then
        source "$PROJECT_ROOT/.env.production"
    fi
    
    # Run main deployment
    main "$@"
fi
