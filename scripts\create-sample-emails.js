const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSampleEmails() {
  try {
    console.log('Creating sample email data...')

    // Get existing email accounts
    const emailAccounts = await prisma.emailAccount.findMany({
      take: 10
    })

    if (emailAccounts.length === 0) {
      console.log('No email accounts found. Please create email accounts first.')
      return
    }

    // Check if emails already exist
    const existingEmails = await prisma.email.findMany()
    
    if (existingEmails.length > 0) {
      console.log('Sample emails already exist. Skipping creation.')
      return
    }

    const sampleEmails = []
    const statuses = ['SENT', 'DELIVERED', 'FAILED', 'QUEUED', 'DRAFT']
    const priorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT']
    
    const subjects = [
      'Welcome to SNPITC Institute',
      'Assignment Submission Reminder',
      'Fee Payment Due Notice',
      'Exam Schedule Update',
      'Library Book Return Notice',
      'Hostel Room Allocation',
      'Course Registration Open',
      'Academic Calendar Update',
      'Scholarship Application',
      'Campus Event Invitation',
      'Grade Report Available',
      'System Maintenance Notice',
      'New Course Announcement',
      'Student Council Elections',
      'Career Guidance Session'
    ]

    const bodyTemplates = [
      'Dear Student,\n\nThis is an important notification regarding your academic activities.\n\nBest regards,\nSNPITC Administration',
      'Hello,\n\nWe hope this email finds you well. Please find the attached information.\n\nThank you,\nAcademic Office',
      'Dear Student,\n\nKindly take note of the following important information.\n\nRegards,\nStudent Services',
      'Hi there,\n\nThis is a friendly reminder about upcoming deadlines.\n\nBest wishes,\nSNPITC Team'
    ]

    // Create 50 sample emails
    for (let i = 0; i < 50; i++) {
      const fromAccount = emailAccounts[Math.floor(Math.random() * emailAccounts.length)]
      const toAccount = emailAccounts[Math.floor(Math.random() * emailAccounts.length)]
      
      // Ensure from and to are different
      if (fromAccount.id === toAccount.id) continue

      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const priority = priorities[Math.floor(Math.random() * priorities.length)]
      const subject = subjects[Math.floor(Math.random() * subjects.length)]
      const body = bodyTemplates[Math.floor(Math.random() * bodyTemplates.length)]
      
      const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date in last 30 days
      const sentAt = (status !== 'QUEUED' && status !== 'DRAFT') ? new Date(createdAt.getTime() + Math.random() * 60 * 60 * 1000) : null
      const deliveredAt = status === 'DELIVERED' ? new Date(sentAt.getTime() + Math.random() * 30 * 60 * 1000) : null

      sampleEmails.push({
        messageId: `<${Date.now()}-${i}@snpitc.in>`,
        subject: subject,
        body: body,
        bodyText: body,
        fromEmail: fromAccount.email,
        fromName: fromAccount.displayName || fromAccount.email,
        status: status,
        priority: priority,
        isRead: Math.random() > 0.3, // 70% chance of being read
        isStarred: Math.random() > 0.8, // 20% chance of being starred
        isDeleted: false,
        spamScore: Math.random() * 10,
        isSpam: Math.random() > 0.9, // 10% chance of being spam
        createdAt: createdAt,
        sentAt: sentAt,
        deliveredAt: deliveredAt,
        senderUserId: fromAccount.createdById
      })
    }

    // Insert sample emails
    for (const email of sampleEmails) {
      const createdEmail = await prisma.email.create({
        data: email
      })

      // Add recipients
      const toAccount = emailAccounts[Math.floor(Math.random() * emailAccounts.length)]
      await prisma.emailRecipient.create({
        data: {
          emailId: createdEmail.id,
          email: toAccount.email,
          name: toAccount.displayName || toAccount.email,
          type: 'TO'
        }
      })

      // Randomly add CC recipients (30% chance)
      if (Math.random() > 0.7) {
        const ccAccount = emailAccounts[Math.floor(Math.random() * emailAccounts.length)]
        if (ccAccount.email !== toAccount.email && ccAccount.email !== email.fromEmail) {
          await prisma.emailRecipient.create({
            data: {
              emailId: createdEmail.id,
              email: ccAccount.email,
              name: ccAccount.displayName || ccAccount.email,
              type: 'CC'
            }
          })
        }
      }
    }

    console.log(`✅ Created ${sampleEmails.length} sample emails`)
    
    // Display summary
    const summary = await prisma.email.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    console.log('\n📊 Email Summary:')
    summary.forEach(item => {
      console.log(`${item.status}: ${item._count.status} emails`)
    })

    console.log('✅ Sample email data creation completed')

  } catch (error) {
    console.error('Error creating sample emails:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSampleEmails()
