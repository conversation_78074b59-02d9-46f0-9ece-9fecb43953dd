// Using built-in fetch (Node.js 18+)

async function testNextAuthAPI() {
  const baseUrl = 'http://localhost:3001'
  
  console.log('🧪 Testing NextAuth API endpoints...\n')

  try {
    // Test 1: Check NextAuth session endpoint
    console.log('1. Testing session endpoint...')
    const sessionResponse = await fetch(`${baseUrl}/api/auth/session`)
    console.log(`   GET /api/auth/session: ${sessionResponse.status}`)
    
    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json()
      console.log('   Session data:', sessionData)
    }

    // Test 2: Check NextAuth providers endpoint
    console.log('\n2. Testing providers endpoint...')
    const providersResponse = await fetch(`${baseUrl}/api/auth/providers`)
    console.log(`   GET /api/auth/providers: ${providersResponse.status}`)
    
    if (providersResponse.ok) {
      const providersData = await providersResponse.json()
      console.log('   Available providers:', Object.keys(providersData))
    }

    // Test 3: Test admin credentials authentication
    console.log('\n3. Testing admin authentication...')
    const adminAuthResponse = await fetch(`${baseUrl}/api/auth/callback/admin-credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: 'admin123',
        csrfToken: 'test', // In real scenario, this would be fetched from CSRF endpoint
      }),
    })
    console.log(`   POST /api/auth/callback/admin-credentials: ${adminAuthResponse.status}`)

    // Test 4: Test student credentials authentication
    console.log('\n4. Testing student authentication...')
    const studentAuthResponse = await fetch(`${baseUrl}/api/auth/callback/student-credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: 'password123',
        csrfToken: 'test',
      }),
    })
    console.log(`   POST /api/auth/callback/student-credentials: ${studentAuthResponse.status}`)

    console.log('\n✅ NextAuth API test completed!')

  } catch (error) {
    console.error('❌ NextAuth API test failed:', error.message)
  }
}

testNextAuthAPI()
