const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function populateContentPages() {
  console.log('🌱 Populating content pages with authentic SNPITC data...')

  try {
    // Get the first admin user to assign as creator
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'ADMIN'
      }
    })

    if (!adminUser) {
      console.error('❌ No admin user found. Please create an admin user first.')
      return
    }

    console.log(`👤 Using admin user: ${adminUser.email}`)

    const pages = [
      {
        title: 'Summary of Trades Affiliated to NCVT',
        slug: 'trades-ncvt',
        description: 'Our institute offers NCVT-affiliated trades that provide industry-recognized certification and excellent career opportunities.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">NCVT Affiliated Trades</h2>
            <p class="mb-6">All trades are approved by the Directorate General of Employment & Training (DGE&T), Government of India since 2009.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">1st Intake</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">2nd Capacity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">3rd Total No. of Units</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DGET Order No.</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ELECTRICIAN</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">02</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">02</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">02</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6/20/35/2009-TC</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8">
              <h3 class="text-lg font-semibold mb-4">Trade Details</h3>
              <div class="bg-blue-50 p-6 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">Electrician Trade</h4>
                <ul class="text-blue-800 space-y-1">
                  <li>• Duration: 2 Years</li>
                  <li>• Total Capacity: 126 sanctioned seats</li>
                  <li>• Shifts: 2 shifts available</li>
                  <li>• Affiliation: NCVT (National Council for Vocational Training)</li>
                  <li>• Established: 2009</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'NCVT Affiliated Trades - S.N. ITI',
        metaDesc: 'View the complete list of NCVT affiliated trades offered at S.N. Private Industrial Training Institute with detailed information about capacity and approval orders.'
      },
      {
        title: 'Infrastructure, Building and Workshop',
        slug: 'infrastructure',
        description: 'Comprehensive overview of our institute\'s infrastructure, building facilities, and workshop areas.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Infrastructure Details</h2>
            <p class="mb-6">Our institute boasts modern infrastructure designed to provide the best learning environment for technical education.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sl. No.</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Area in Sq. Mt.</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">a</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Total Area</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">557 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">b</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Covered Area</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">457 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">c</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Total Class Room Area</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">d</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Total Workshop Area</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">e</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Drawing Hall</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Included in trade workshop</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">f</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Audio Visual Hall</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">g</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Computer Lab</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">380 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">h</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Play Area</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">Under Development</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">i</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Library</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">27.5 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Available</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Total Area</h3>
                <p class="text-3xl font-bold text-blue-600">557</p>
                <p class="text-sm text-blue-700">Square Meters</p>
              </div>
              <div class="bg-green-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-green-900 mb-2">Covered Area</h3>
                <p class="text-3xl font-bold text-green-600">457</p>
                <p class="text-sm text-green-700">Square Meters</p>
              </div>
              <div class="bg-purple-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-purple-900 mb-2">Workshop Area</h3>
                <p class="text-3xl font-bold text-purple-600">200</p>
                <p class="text-sm text-purple-700">Square Meters</p>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Infrastructure & Building - S.N. ITI',
        metaDesc: 'Detailed overview of infrastructure, building facilities, and workshop areas at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Fee Structure',
        slug: 'fee-structure',
        description: 'Complete fee structure and payment details for all courses offered at our institute.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Fee Structure</h2>
            <p class="mb-6">Our institute offers affordable and transparent fee structure for quality technical education.</p>

            <div class="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 class="text-lg font-semibold text-blue-900 mb-4">Download Official Fee Structure</h3>
              <p class="text-blue-800 mb-4">For the most current and detailed fee information, please download our official fee structure document.</p>
              <a href="/downloads/Fee sniti.pdf" target="_blank" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download Fee Structure PDF
              </a>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 class="text-lg font-semibold mb-4">General Information</h3>
                <ul class="space-y-2 text-gray-600">
                  <li>• Fees are to be paid semester-wise</li>
                  <li>• Late payment charges may apply</li>
                  <li>• Scholarships available for eligible students</li>
                  <li>• Fee concessions for economically weaker sections</li>
                  <li>• Payment can be made in cash or by demand draft</li>
                </ul>
              </div>
              <div>
                <h3 class="text-lg font-semibold mb-4">Payment Methods</h3>
                <ul class="space-y-2 text-gray-600">
                  <li>• Cash payment at institute office</li>
                  <li>• Demand Draft in favor of "S.N. ITI"</li>
                  <li>• Bank transfer (details available on request)</li>
                  <li>• Online payment (coming soon)</li>
                </ul>
              </div>
            </div>

            <div class="mt-8 p-6 bg-yellow-50 rounded-lg">
              <h3 class="text-lg font-semibold text-yellow-900 mb-2">Important Note</h3>
              <p class="text-yellow-800">Fee structure is subject to change as per government regulations and NCVT guidelines. Students are advised to confirm the current fee structure at the time of admission.</p>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Fee Structure - S.N. ITI',
        metaDesc: 'Complete fee structure and payment information for courses at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Summary of Trades Affiliated To SCVT',
        slug: 'trades-scvt',
        description: 'Information about trades affiliated to State Council for Vocational Training (SCVT).',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Summary of Trades Affiliated to SCVT</h2>
            <p class="mb-6">State Council for Vocational Training (SCVT) affiliated trades information.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">1st Intake</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">2nd Capacity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">3rd Total No. of Units</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                      <div class="text-center py-8">
                        <p class="text-gray-500">Currently no SCVT affiliated trades available.</p>
                        <p class="text-sm text-gray-400 mt-2">The institute is focusing on NCVT affiliated programs.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 class="text-lg font-semibold text-blue-900 mb-2">Future Plans</h3>
              <p class="text-blue-800">The institute is exploring opportunities to introduce SCVT affiliated trades to provide more diverse educational options for students. Stay tuned for updates on new trade offerings.</p>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'SCVT Affiliated Trades - S.N. ITI',
        metaDesc: 'Information about State Council for Vocational Training (SCVT) affiliated trades at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Application Format',
        slug: 'application-format',
        description: 'Download application forms and admission documents for various courses.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Application Format</h2>
            <p class="mb-6">Download the required application forms for admission to various courses offered at our institute.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Course</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tentative Date of Course</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">When to Apply</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application Format</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Electrician</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 Years</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">August</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10th pass</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/downloads/ApplicationForm.pdf" target="_blank" class="hover:underline">
                        Click Here to Download Application Form
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Application Process</h3>
                <ol class="text-green-800 space-y-2">
                  <li>1. Download the application form</li>
                  <li>2. Fill all required details carefully</li>
                  <li>3. Attach necessary documents</li>
                  <li>4. Submit at institute office</li>
                  <li>5. Pay the application fee</li>
                </ol>
              </div>
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">Required Documents</h3>
                <ul class="text-yellow-800 space-y-2">
                  <li>• 10th class mark sheet</li>
                  <li>• Transfer certificate</li>
                  <li>• Character certificate</li>
                  <li>• Passport size photographs</li>
                  <li>• Caste certificate (if applicable)</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Application Format - S.N. ITI',
        metaDesc: 'Download application forms and admission documents for courses at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Trade Specific Infrastructure',
        slug: 'trade-infrastructure',
        description: 'Detailed information about trade-specific infrastructure, building and workshop facilities.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Trade Specific Infrastructure Building and Workshop</h2>
            <p class="mb-6">Comprehensive details of our trade-specific infrastructure and workshop facilities designed for practical training.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Room Area Per Unit</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Class room area for trade</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Workshop area per unit</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Workshop area for Trade</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Photo</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Electrician (Existing)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">02</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">21.06 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">21.06 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">99.06 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">99.06 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/infrastructure/workshop.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Electrician (App. for Affi.)</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">21 Sq.Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">100 SqMt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">100 Sq. Mt.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/infrastructure/workshop.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Total Classroom Area</h3>
                <p class="text-3xl font-bold text-blue-600">63.06</p>
                <p class="text-sm text-blue-700">Square Meters</p>
              </div>
              <div class="bg-green-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-green-900 mb-2">Total Workshop Area</h3>
                <p class="text-3xl font-bold text-green-600">199.06</p>
                <p class="text-sm text-green-700">Square Meters</p>
              </div>
              <div class="bg-purple-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-purple-900 mb-2">Total Units</h3>
                <p class="text-3xl font-bold text-purple-600">6</p>
                <p class="text-sm text-purple-700">Training Units</p>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Trade Specific Infrastructure - S.N. ITI',
        metaDesc: 'Detailed information about trade-specific infrastructure, building and workshop facilities at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Trades Affiliated To NCVT and SCVT',
        slug: 'trades-ncvt-scvt',
        description: 'Complete overview of all trades affiliated to both NCVT and SCVT at our institute.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">NCVT & SCVT Affiliated Trades</h2>
            <p class="mb-6">S.N. Private Industrial Training Institute is affiliated with both National Council for Vocational Training (NCVT) and State Council for Vocational Training (SCVT), providing comprehensive technical education opportunities.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-blue-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-blue-900 mb-4">NCVT Affiliated Trades</h3>
                <div class="space-y-3">
                  <div class="bg-white p-4 rounded">
                    <h4 class="font-semibold">Electrician</h4>
                    <p class="text-sm text-gray-600">Duration: 2 Years | Capacity: 126 seats</p>
                    <p class="text-sm text-gray-600">Order No: 6/20/35/2009-TC</p>
                  </div>
                </div>
              </div>
              
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-green-900 mb-4">SCVT Affiliated Trades</h3>
                <div class="space-y-3">
                  <div class="bg-white p-4 rounded">
                    <p class="text-sm text-gray-600">Currently expanding SCVT trade offerings</p>
                    <p class="text-sm text-gray-600">Contact institute for latest updates</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="mt-8">
              <h3 class="text-lg font-semibold mb-4">Key Features</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded">
                  <h4 class="font-semibold">Industry Recognition</h4>
                  <p class="text-sm text-gray-600">Certificates recognized nationwide</p>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded">
                  <h4 class="font-semibold">Practical Training</h4>
                  <p class="text-sm text-gray-600">Hands-on workshop experience</p>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded">
                  <h4 class="font-semibold">Placement Support</h4>
                  <p class="text-sm text-gray-600">Job assistance after completion</p>
                </div>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'NCVT & SCVT Affiliated Trades - S.N. ITI',
        metaDesc: 'Comprehensive overview of all NCVT and SCVT affiliated trades at S.N. Private Industrial Training Institute.'
      }
    ]

    // Create pages
    for (const pageData of pages) {
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })

      if (existingPage) {
        console.log(`📄 Updating existing page: ${pageData.title}`)
        await prisma.page.update({
          where: { slug: pageData.slug },
          data: {
            ...pageData,
            updatedAt: new Date()
          }
        })
      } else {
        console.log(`📄 Creating new page: ${pageData.title}`)
        await prisma.page.create({
          data: {
            ...pageData,
            createdById: adminUser.id,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
    }

    console.log('🎉 Content pages populated successfully!')
    console.log(`📊 Total pages processed: ${pages.length}`)

  } catch (error) {
    console.error('❌ Error populating content pages:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the population function
populateContentPages()
  .catch((error) => {
    console.error('❌ Population failed:', error)
    process.exit(1)
  })
