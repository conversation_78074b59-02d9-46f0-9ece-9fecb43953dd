import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

// Create two separate Prisma clients
const sqlitePrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./dev.db'
    }
  }
})

const postgresPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

async function migrateData() {
  console.log('🚀 Starting data migration from SQLite to Supabase PostgreSQL...')
  
  try {
    // First, ensure the PostgreSQL database is ready
    await postgresPrisma.$connect()
    console.log('✅ Connected to PostgreSQL database')

    // Migrate Users
    console.log('📦 Migrating users...')
    const users = await sqlitePrisma.user.findMany()
    for (const user of users) {
      await postgresPrisma.user.upsert({
        where: { id: user.id },
        update: {},
        create: {
          id: user.id,
          email: user.email,
          name: user.name,
          password: user.password,
          role: user.role,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${users.length} users`)

    // Migrate Pages
    console.log('📦 Migrating pages...')
    const pages = await sqlitePrisma.page.findMany()
    for (const page of pages) {
      await postgresPrisma.page.upsert({
        where: { id: page.id },
        update: {},
        create: {
          id: page.id,
          title: page.title,
          slug: page.slug,
          description: page.description,
          content: page.content,
          metaTitle: page.metaTitle,
          metaDesc: page.metaDesc,
          status: page.status,
          parentId: page.parentId,
          order: page.order,
          navigationCategory: page.navigationCategory,
          navigationOrder: page.navigationOrder,
          createdAt: page.createdAt,
          updatedAt: page.updatedAt,
          createdById: page.createdById
        }
      })
    }
    console.log(`✅ Migrated ${pages.length} pages`)

    // Migrate Content
    console.log('📦 Migrating content...')
    const contents = await sqlitePrisma.content.findMany()
    for (const content of contents) {
      await postgresPrisma.content.upsert({
        where: { id: content.id },
        update: {},
        create: {
          id: content.id,
          type: content.type,
          title: content.title,
          content: content.content,
          data: content.data,
          order: content.order,
          pageId: content.pageId,
          createdAt: content.createdAt,
          updatedAt: content.updatedAt,
          createdById: content.createdById
        }
      })
    }
    console.log(`✅ Migrated ${contents.length} content items`)

    // Migrate Media
    console.log('📦 Migrating media...')
    const media = await sqlitePrisma.media.findMany()
    for (const item of media) {
      await postgresPrisma.media.upsert({
        where: { id: item.id },
        update: {},
        create: {
          id: item.id,
          filename: item.filename,
          originalName: item.originalName,
          mimeType: item.mimeType,
          size: item.size,
          url: item.url,
          alt: item.alt,
          caption: item.caption,
          category: item.category,
          tags: item.tags,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          createdById: item.createdById
        }
      })
    }
    console.log(`✅ Migrated ${media.length} media items`)

    // Migrate Contact Messages
    console.log('📦 Migrating contact messages...')
    const messages = await sqlitePrisma.contactMessage.findMany()
    for (const message of messages) {
      await postgresPrisma.contactMessage.upsert({
        where: { id: message.id },
        update: {},
        create: {
          id: message.id,
          name: message.name,
          email: message.email,
          contact: message.contact,
          subject: message.subject,
          message: message.message,
          isRead: message.isRead,
          createdAt: message.createdAt,
          updatedAt: message.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${messages.length} contact messages`)

    // Migrate Faculty
    console.log('📦 Migrating faculty...')
    const faculty = await sqlitePrisma.faculty.findMany()
    for (const member of faculty) {
      await postgresPrisma.faculty.upsert({
        where: { id: member.id },
        update: {},
        create: {
          id: member.id,
          name: member.name,
          designation: member.designation,
          department: member.department,
          email: member.email,
          phone: member.phone,
          photoUrl: member.photoUrl,
          bio: member.bio,
          order: member.order,
          isActive: member.isActive,
          createdAt: member.createdAt,
          updatedAt: member.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${faculty.length} faculty members`)

    // Migrate Settings
    console.log('📦 Migrating settings...')
    const settings = await sqlitePrisma.setting.findMany()
    for (const setting of settings) {
      await postgresPrisma.setting.upsert({
        where: { id: setting.id },
        update: {},
        create: {
          id: setting.id,
          key: setting.key,
          value: setting.value,
          type: setting.type,
          createdAt: setting.createdAt,
          updatedAt: setting.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${settings.length} settings`)

    // Migrate Navigation Items
    console.log('📦 Migrating navigation items...')
    const navItems = await sqlitePrisma.navigationItem.findMany()
    for (const item of navItems) {
      await postgresPrisma.navigationItem.upsert({
        where: { id: item.id },
        update: {},
        create: {
          id: item.id,
          title: item.title,
          href: item.href,
          parentId: item.parentId,
          order: item.order,
          isVisible: item.isVisible,
          linkType: item.linkType,
          target: item.target,
          description: item.description,
          icon: item.icon,
          cssClass: item.cssClass,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }
      })
    }
    console.log(`✅ Migrated ${navItems.length} navigation items`)

    // Create initial email server configuration
    console.log('📦 Creating email server configuration...')
    const domain = process.env.EMAIL_DOMAIN || 'institute.edu'
    await postgresPrisma.emailServerConfig.upsert({
      where: { domain },
      update: {},
      create: {
        domain,
        smtpHost: `mail.${domain}`,
        smtpPort: 587,
        smtpSecurity: 'STARTTLS',
        imapHost: `mail.${domain}`,
        imapPort: 993,
        imapSecurity: 'SSL',
        pop3Host: `mail.${domain}`,
        pop3Port: 995,
        pop3Security: 'SSL',
        defaultQuota: BigInt(**********), // 1GB
        maxAttachmentSize: ********, // 25MB
        spamThreshold: 5.0,
        enableSpamFilter: true
      }
    })
    console.log('✅ Created email server configuration')

    // Create admin email account
    console.log('📦 Creating admin email account...')
    const adminUser = await postgresPrisma.user.findFirst({
      where: { role: 'ADMIN' }
    })
    
    if (adminUser) {
      const adminEmail = process.env.EMAIL_ADMIN || `admin@${domain}`
      const adminPassword = await bcrypt.hash('admin123', 12)
      
      await postgresPrisma.emailAccount.upsert({
        where: { email: adminEmail },
        update: {},
        create: {
          email: adminEmail,
          password: adminPassword,
          type: 'ADMIN',
          firstName: 'Admin',
          lastName: 'User',
          displayName: 'Administrator',
          department: 'Administration',
          createdById: adminUser.id
        }
      })
      console.log(`✅ Created admin email account: ${adminEmail}`)
    }

    console.log('🎉 Migration completed successfully!')
    console.log('📝 Next steps:')
    console.log('1. Update your .env files with Supabase credentials')
    console.log('2. Run: npm run db:generate')
    console.log('3. Test the application with the new database')
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  } finally {
    await sqlitePrisma.$disconnect()
    await postgresPrisma.$disconnect()
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateData()
}

export default migrateData
