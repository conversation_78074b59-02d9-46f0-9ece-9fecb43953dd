import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function createTables() {
  try {
    console.log('🔧 Creating essential tables in Supabase...')

    // Create Users table
    console.log('👥 Creating users table...')
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "users" (
        "id" TEXT NOT NULL,
        "email" TEXT NOT NULL,
        "name" TEXT,
        "password" TEXT NOT NULL,
        "role" TEXT NOT NULL DEFAULT 'EDITOR',
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        CONSTRAINT "users_pkey" PRIMARY KEY ("id")
      );
    `)

    await prisma.$executeRawUnsafe(`
      CREATE UNIQUE INDEX IF NOT EXISTS "users_email_key" ON "users"("email");
    `)

    // Create EmailAccount table
    console.log('📧 Creating email_accounts table...')
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "email_accounts" (
        "id" TEXT NOT NULL,
        "email" TEXT NOT NULL,
        "password" TEXT NOT NULL,
        "type" TEXT NOT NULL DEFAULT 'STUDENT',
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "quota" INTEGER NOT NULL DEFAULT **********,
        "usedQuota" INTEGER NOT NULL DEFAULT 0,
        "firstName" TEXT,
        "lastName" TEXT,
        "displayName" TEXT,
        "department" TEXT,
        "studentId" TEXT,
        "signature" TEXT,
        "autoReply" BOOLEAN NOT NULL DEFAULT false,
        "autoReplyMessage" TEXT,
        "forwardingEmail" TEXT,
        "lastLogin" TIMESTAMP(3),
        "loginAttempts" INTEGER NOT NULL DEFAULT 0,
        "lockedUntil" TIMESTAMP(3),
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        "createdById" TEXT,
        CONSTRAINT "email_accounts_pkey" PRIMARY KEY ("id")
      );
    `)

    await prisma.$executeRawUnsafe(`
      CREATE UNIQUE INDEX IF NOT EXISTS "email_accounts_email_key" ON "email_accounts"("email");
    `)

    // Create PaymentCatalogItem table
    console.log('💰 Creating payment_catalog_items table...')
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "payment_catalog_items" (
        "id" TEXT NOT NULL,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "feeType" TEXT NOT NULL,
        "amount" DECIMAL(10,2) NOT NULL,
        "currency" TEXT NOT NULL DEFAULT 'INR',
        "category" TEXT,
        "subcategory" TEXT,
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "isRecurring" BOOLEAN NOT NULL DEFAULT false,
        "recurringInterval" TEXT,
        "lateFee" DECIMAL(10,2) NOT NULL DEFAULT 0,
        "discountPercentage" DECIMAL(5,2) NOT NULL DEFAULT 0,
        "dueDate" TIMESTAMP(3),
        "applicableFor" TEXT NOT NULL DEFAULT 'STUDENT',
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        "deletedAt" TIMESTAMP(3),
        "createdById" TEXT,
        CONSTRAINT "payment_catalog_items_pkey" PRIMARY KEY ("id")
      );
    `)

    // Create Payment table
    console.log('💳 Creating payments table...')
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "payments" (
        "id" TEXT NOT NULL,
        "transactionId" TEXT NOT NULL,
        "studentId" TEXT NOT NULL,
        "catalogItemId" TEXT,
        "amount" DECIMAL(10,2) NOT NULL,
        "currency" TEXT NOT NULL DEFAULT 'INR',
        "feeType" TEXT NOT NULL,
        "description" TEXT NOT NULL,
        "gateway" TEXT NOT NULL,
        "gatewayTxnId" TEXT,
        "gatewayResponse" TEXT,
        "additionalFee" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
        "totalAmount" DECIMAL(10,2) NOT NULL,
        "status" TEXT NOT NULL DEFAULT 'PENDING',
        "failureReason" TEXT,
        "receiptNumber" TEXT,
        "receiptUrl" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        "completedAt" TIMESTAMP(3),
        CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
      );
    `)

    await prisma.$executeRawUnsafe(`
      CREATE UNIQUE INDEX IF NOT EXISTS "payments_transactionId_key" ON "payments"("transactionId");
    `)

    // Create Settings table
    console.log('⚙️ Creating settings table...')
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "settings" (
        "id" TEXT NOT NULL,
        "key" TEXT NOT NULL,
        "value" TEXT NOT NULL,
        "type" TEXT NOT NULL DEFAULT 'STRING',
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        CONSTRAINT "settings_pkey" PRIMARY KEY ("id")
      );
    `)

    await prisma.$executeRawUnsafe(`
      CREATE UNIQUE INDEX IF NOT EXISTS "settings_key_key" ON "settings"("key");
    `)

    console.log('✅ Essential tables created successfully!')

    // Test table creation by inserting a test record
    console.log('🧪 Testing table functionality...')
    
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'test123',
        role: 'ADMIN'
      }
    })

    console.log('✅ Test user created:', testUser.email)

    // Clean up test user
    await prisma.user.delete({
      where: { email: '<EMAIL>' }
    })

    console.log('✅ Test cleanup completed')

  } catch (error) {
    console.error('❌ Error creating tables:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

createTables()
  .then(() => {
    console.log('🎉 Supabase tables setup completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Table creation failed:', error.message)
    process.exit(1)
  })
