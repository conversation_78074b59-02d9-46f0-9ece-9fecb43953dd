{"buildCommand": "npm run vercel-build", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "rewrites": [{"source": "/admin/:path*", "destination": "/admin/:path*"}], "env": {"PRISMA_GENERATE_SKIP_AUTOINSTALL": "false", "DATABASE_URL": "file:/tmp/prod.db"}, "build": {"env": {"PRISMA_GENERATE_SKIP_AUTOINSTALL": "false", "DATABASE_URL": "file:/tmp/prod.db", "NEXT_PHASE": "phase-production-build"}}}