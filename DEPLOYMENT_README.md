# SNPITC Website - Quick Deployment Guide

## 🚀 Quick Start (5 Minutes)

### Option 1: Automated Setup Script

**Linux/Mac:**
```bash
chmod +x scripts/deploy-setup.sh
./scripts/deploy-setup.sh
```

**Windows:**
```cmd
scripts\deploy-setup.bat
```

### Option 2: Manual Setup

```bash
# 1. Install dependencies
npm install

# 2. Setup environment
cp .env.example .env.local
# Edit .env.local with your settings

# 3. Setup database
npx prisma generate
npx prisma db push
npm run db:seed

# 4. Build and test
npm run build
npm run dev
```

## 🌐 Hosting Options (Free)

| Platform | Best For | Setup Time | Database | Custom Domain |
|----------|----------|------------|----------|---------------|
| **Vercel** | Performance | 5 min | External | ✅ Free |
| **Render** | Full-stack | 10 min | Included | ✅ Free |
| **Railway** | Developers | 5 min | Included | ✅ Free |

## 📋 Pre-Deployment Checklist

- [ ] Environment variables configured
- [ ] Database setup completed
- [ ] Build process working (`npm run build`)
- [ ] Admin credentials secured
- [ ] Domain/hosting platform chosen

## 🔗 Quick Deploy Links

### Vercel (Recommended for Performance)
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/snpitc-website)

**Required Environment Variables:**
```env
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=your-32-char-secret
NEXTAUTH_URL=https://your-app.vercel.app
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-password
```

### Render (Recommended for Beginners)
1. Fork this repository
2. Connect to [render.com](https://render.com)
3. Create PostgreSQL database
4. Create web service
5. Add environment variables

### Railway (Recommended for Developers)
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/yourusername/snpitc-website)

## 🗄️ Database Options

### Free PostgreSQL Providers
- **Neon** (Recommended): 1GB free, excellent performance
- **Supabase**: 500MB free, includes auth and storage
- **Railway**: Included with hosting, $5 monthly credit
- **Render**: 1GB free with hosting

## 🔧 Common Issues & Solutions

### Build Failures
```bash
# Clear cache and rebuild
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### Database Connection Issues
```env
# Ensure SSL mode for production
DATABASE_URL="**********************************************"
```

### Environment Variable Issues
```bash
# Check all required variables are set
echo $DATABASE_URL
echo $NEXTAUTH_SECRET
echo $NEXTAUTH_URL
```

## 📱 Mobile & Performance

The website is optimized for:
- ✅ Mobile responsiveness
- ✅ Fast loading (WebP images)
- ✅ SEO optimization
- ✅ Accessibility (WCAG 2.1 AA)

## 🔒 Security Features

- ✅ NextAuth.js authentication
- ✅ Role-based access control
- ✅ CSRF protection
- ✅ SQL injection prevention
- ✅ XSS protection

## 📊 What's Included

### Frontend Features
- Modern Next.js 15 with App Router
- Responsive design (mobile-first)
- Professional UI components
- Image optimization
- SEO optimization

### Admin Panel
- User management
- Content management (CMS)
- Media management
- Navigation management
- Analytics dashboard

### Database Schema
- Users and authentication
- Pages and content
- Media and file management
- Contact messages
- Faculty management
- Site settings

## 🎯 Production Recommendations

### For Small Organizations (Free)
```
Platform: Render
Database: Render PostgreSQL (free)
Storage: Local uploads (temporary)
Domain: platform.onrender.com
```

### For Professional Use ($20-40/month)
```
Platform: Vercel Pro
Database: Neon Pro
Storage: Cloudinary
Domain: Custom domain
```

## 📞 Support

### Documentation
- [Complete Hosting Guide](HOSTING_DEPLOYMENT.md)
- [Production Deployment](PRODUCTION_DEPLOYMENT.md)
- [Next.js Documentation](https://nextjs.org/docs)

### Community
- [Next.js Discord](https://nextjs.org/discord)
- [Vercel Community](https://github.com/vercel/vercel/discussions)

### Emergency
- Check platform status pages
- Review error logs in hosting dashboard
- Verify environment variables

## 🎉 Success Metrics

After deployment, verify:
- [ ] Website loads in under 3 seconds
- [ ] Admin panel accessible
- [ ] All pages render correctly
- [ ] Mobile experience smooth
- [ ] Contact forms working
- [ ] Search functionality working

---

## 🚀 Ready to Deploy?

1. **Choose your platform** from the options above
2. **Run the setup script** or follow manual steps
3. **Follow the detailed guide** in [HOSTING_DEPLOYMENT.md](HOSTING_DEPLOYMENT.md)
4. **Test thoroughly** before going live
5. **Monitor performance** after deployment

**Need help?** Check the [complete deployment guide](HOSTING_DEPLOYMENT.md) for detailed instructions.

---

*The SNPITC website is production-ready and optimized for deployment on modern hosting platforms. Choose the option that best fits your needs and budget.*
