# 📧 IMAP API Simulation Documentation

## Overview

The IMAP API Simulation provides standard IMAP4rev1 protocol compatibility for email clients while operating over HTTP/HTTPS. This allows email clients like Outlook, Thunderbird, and mobile apps to access emails through our system using familiar IMAP commands and responses.

## 🚀 Quick Start

### Basic IMAP Session Flow

1. **Initialize Session**: `POST /api/imap`
2. **Send Commands**: `POST /api/imap` with IMAP commands
3. **Authenticate**: Send `LOGIN` command
4. **Access Mailboxes**: Use `LIST`, `SELECT`, `FETCH` commands
5. **Close Session**: Send `LOGOUT` command

### Example: Reading Emails

```javascript
// 1. Initialize session
const initResponse = await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
})
const { sessionId } = await initResponse.json()

// 2. Get capabilities
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A001 CAPABILITY'
  })
})

// 3. Login
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A002 LOGIN <EMAIL> password'
  })
})

// 4. List folders
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A003 LIST "" "*"'
  })
})

// 5. Select INBOX
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A004 SELECT INBOX'
  })
})

// 6. Fetch messages
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A005 FETCH 1:10 (FLAGS ENVELOPE)'
  })
})

// 7. Logout
await fetch('/api/imap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'A006 LOGOUT'
  })
})
```

## 📡 API Endpoints

### 1. Main IMAP Endpoint

**`POST /api/imap`** - IMAP Protocol Handler

**Request Body:**
```json
{
  "sessionId": "optional-session-id",
  "command": "IMAP command string"
}
```

**Response:**
```json
{
  "sessionId": "session-identifier",
  "responses": [
    {
      "type": "untagged|tagged|continuation",
      "tag": "A001",
      "status": "OK|NO|BAD",
      "command": "CAPABILITY",
      "data": "response data",
      "message": "response message"
    }
  ],
  "state": "NOT_AUTHENTICATED|AUTHENTICATED|SELECTED|LOGOUT",
  "authenticated": true
}
```

**`GET /api/imap`** - Service Information

Returns IMAP service capabilities and limits.

### 2. Authentication Endpoint

**`POST /api/imap/auth`** - IMAP Authentication

**Request Body:**
```json
{
  "sessionId": "session-id",
  "method": "PLAIN|LOGIN",
  "credentials": {
    "username": "<EMAIL>",
    "password": "password",
    "encoded": "base64-encoded-credentials"
  }
}
```

**`GET /api/imap/auth`** - Authentication Methods

Returns supported authentication methods and usage examples.

### 3. Configuration Endpoint

**`GET /api/imap/config`** - Email Client Configuration

**Query Parameters:**
- `format`: `json|xml|autodiscover|thunderbird`
- `client`: `outlook|thunderbird|apple|android|ios`

**Examples:**
- `/api/imap/config?client=outlook` - Outlook-specific settings
- `/api/imap/config?format=xml` - Autodiscover XML
- `/api/imap/config?format=thunderbird` - Thunderbird autoconfig

### 4. Testing Endpoint

**`POST /api/imap/test`** - IMAP Protocol Testing

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "password",
  "testMailbox": "INBOX"
}
```

## 🔧 IMAP Commands Supported

### Connection Management
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `CAPABILITY` | Get server capabilities | none | `A001 CAPABILITY` |
| `LOGIN` | Authenticate user | username password | `A002 LOGIN <EMAIL> pass` |
| `LOGOUT` | End session | none | `A003 LOGOUT` |
| `NOOP` | No operation | none | `A004 NOOP` |

### Folder Operations
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `LIST` | List folders | reference mailbox | `A005 LIST "" "*"` |
| `LSUB` | List subscribed folders | reference mailbox | `A006 LSUB "" "*"` |
| `SELECT` | Select folder (read-write) | mailbox | `A007 SELECT INBOX` |
| `EXAMINE` | Select folder (read-only) | mailbox | `A008 EXAMINE INBOX` |
| `CREATE` | Create folder | mailbox | `A009 CREATE "New Folder"` |
| `DELETE` | Delete folder | mailbox | `A010 DELETE "Old Folder"` |
| `RENAME` | Rename folder | old new | `A011 RENAME "Old" "New"` |
| `SUBSCRIBE` | Subscribe to folder | mailbox | `A012 SUBSCRIBE INBOX` |
| `UNSUBSCRIBE` | Unsubscribe from folder | mailbox | `A013 UNSUBSCRIBE Spam` |

### Message Operations
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `FETCH` | Retrieve messages | sequence items | `A014 FETCH 1:5 (FLAGS ENVELOPE)` |
| `SEARCH` | Search messages | criteria | `A015 SEARCH UNSEEN` |
| `STORE` | Update message flags | sequence flags | `A016 STORE 1 +FLAGS (\\Seen)` |
| `EXPUNGE` | Remove deleted messages | none | `A017 EXPUNGE` |
| `CLOSE` | Close selected folder | none | `A018 CLOSE` |

### Additional Commands
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `NAMESPACE` | Get namespace info | none | `A019 NAMESPACE` |
| `STATUS` | Get folder status | mailbox items | `A020 STATUS INBOX (MESSAGES)` |
| `APPEND` | Add message to folder | mailbox message | `A021 APPEND INBOX message` |

## 📋 IMAP Response Types

### Status Responses
| Status | Meaning | Description |
|--------|---------|-------------|
| OK | Success | Command completed successfully |
| NO | Failure | Command failed but connection remains |
| BAD | Error | Command syntax error or protocol violation |
| PREAUTH | Pre-authenticated | Connection is already authenticated |
| BYE | Goodbye | Server is closing connection |

### Server Responses
| Response | Description | Example |
|----------|-------------|---------|
| CAPABILITY | Server capabilities | `* CAPABILITY IMAP4rev1 STARTTLS` |
| LIST | Folder listing | `* LIST () "/" INBOX` |
| EXISTS | Message count | `* 42 EXISTS` |
| RECENT | Recent messages | `* 3 RECENT` |
| FLAGS | Available flags | `* FLAGS (\\Seen \\Answered)` |
| FETCH | Message data | `* 1 FETCH (FLAGS (\\Seen))` |
| SEARCH | Search results | `* SEARCH 1 3 5` |

## 🔐 Authentication

### LOGIN Authentication
```
A001 LOGIN username password
```

### AUTHENTICATE PLAIN
```
A002 AUTHENTICATE PLAIN
+ 
dXNlcm5hbWUAdXNlcm5hbWUAcGFzc3dvcmQ=
```

### AUTHENTICATE LOGIN
```
A003 AUTHENTICATE LOGIN
+ VXNlcm5hbWU6
dXNlcm5hbWU=
+ UGFzc3dvcmQ6
cGFzc3dvcmQ=
```

## 📱 Email Client Configuration

### Microsoft Outlook

1. **Manual Setup:**
   - Server: `your-domain.com/api/imap`
   - Port: 993
   - Encryption: SSL/TLS
   - Authentication: Normal password

2. **Autodiscover:**
   - URL: `/api/imap/config?format=autodiscover`

### Mozilla Thunderbird

1. **Manual Setup:**
   - IMAP Server: `your-domain.com/api/imap`
   - Port: 993
   - Security: SSL/TLS
   - Authentication: Normal password

2. **Autoconfig:**
   - URL: `/api/imap/config?format=thunderbird`

### Apple Mail (iOS/macOS)

1. **Manual Setup:**
   - IMAP Server: `your-domain.com/api/imap`
   - Port: 993
   - SSL: Yes
   - Authentication: Password

### Android Email

1. **Manual Setup:**
   - Incoming server: `your-domain.com/api/imap`
   - Port: 993
   - Security type: SSL/TLS
   - Authentication: Normal password

## 🛡️ Security Features

### Encryption
- All communications over HTTPS
- TLS/SSL support simulation
- Secure credential transmission

### Authentication
- Required for all operations except CAPABILITY
- Support for PLAIN and LOGIN methods
- Session-based authentication

### Access Control
- User can only access their own emails
- Folder-level permissions
- Message-level security

### Rate Limiting
- Protection against brute force attacks
- Session timeout (30 minutes)
- Connection limits per IP

## 📊 Limitations & Considerations

### Current Limitations
- HTTP-based simulation (not raw TCP IMAP)
- Session storage in memory (use Redis for production)
- Basic authentication methods only
- Simplified folder structure
- No server-side search optimization

### Production Recommendations
- Use Redis for session storage
- Implement proper password hashing
- Add comprehensive logging
- Set up monitoring and alerts
- Configure proper rate limiting
- Implement folder synchronization

## 🔍 Troubleshooting

### Common Issues

1. **Session Expired**
   - Sessions timeout after 30 minutes
   - Create new session if expired

2. **Authentication Failed**
   - Verify credentials are correct
   - Ensure account exists and is active

3. **No Mailbox Selected**
   - Must SELECT a mailbox before message operations
   - Use SELECT or EXAMINE command

4. **Invalid Command Sequence**
   - Follow proper IMAP state transitions
   - Check command requirements for current state

5. **Message Not Found**
   - Verify message sequence numbers
   - Check if messages were expunged

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_IMAP=true
```

## 📚 Additional Resources

- [RFC 3501 - IMAP4rev1](https://tools.ietf.org/html/rfc3501)
- [RFC 2595 - Using TLS with IMAP](https://tools.ietf.org/html/rfc2595)
- [RFC 4616 - PLAIN SASL Mechanism](https://tools.ietf.org/html/rfc4616)

## 🆘 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: `/api/imap/config`
- Service Status: `/api/imap` (GET)
- Testing: `/api/imap/test` (Admin only)
