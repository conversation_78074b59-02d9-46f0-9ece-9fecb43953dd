import { prisma } from '@/lib/prisma'
import Header from './header'

interface NavigationItem {
  id: string
  title: string
  href?: string | null
  parentId?: string | null
  order: number
  isVisible: boolean
  linkType: string
  target: string
  children?: NavigationItem[]
}

interface MenuItem {
  title: string
  href?: string
  children?: MenuItem[]
}

async function getNavigationItems(): Promise<NavigationItem[]> {
  try {
    const items = await prisma.navigationItem.findMany({
      where: {
        isVisible: true
      },
      orderBy: [
        { order: 'asc' },
        { title: 'asc' }
      ]
    })

    // Build hierarchy
    const itemMap = new Map<string, NavigationItem>()
    const rootItems: NavigationItem[] = []

    // First pass: create map of all items
    items.forEach(item => {
      itemMap.set(item.id, { ...item, children: [] })
    })

    // Second pass: build hierarchy
    items.forEach(item => {
      const navItem = itemMap.get(item.id)!
      
      if (item.parentId && itemMap.has(item.parentId)) {
        const parent = itemMap.get(item.parentId)!
        if (!parent.children) parent.children = []
        parent.children.push(navItem)
      } else {
        rootItems.push(navItem)
      }
    })

    return rootItems
  } catch (error) {
    console.error('Error fetching navigation items:', error)
    return []
  }
}

function convertToMenuItems(navItems: NavigationItem[]): MenuItem[] {
  return navItems.map(item => ({
    title: item.title,
    href: item.href || undefined,
    children: item.children && item.children.length > 0 
      ? convertToMenuItems(item.children)
      : undefined
  }))
}

export default async function NavigationProvider() {
  const navigationItems = await getNavigationItems()
  const menuItems = convertToMenuItems(navigationItems)

  return <Header navigation={menuItems} />
}
