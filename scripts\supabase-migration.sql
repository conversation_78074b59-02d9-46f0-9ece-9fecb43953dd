-- Supabase Migration Script for SNPITC System
-- Run this script in the Supabase SQL Editor

-- <PERSON>reate enums first
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'EDITOR', 'VIEWER');
CREATE TYPE "AccountType" AS ENUM ('STUDENT', 'ADMIN', 'STAFF');
CREATE TYPE "FeeType" AS ENUM ('ADMISSION', 'SEMESTER', 'EXAM', 'LIBRARY', 'HOSTEL', 'TRANSPORT', 'OTHER');
CREATE TYPE "PaymentStatus" AS ENUM ('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED');
CREATE TYPE "PaymentGateway" AS ENUM ('RAZORPAY', 'STRIPE', 'PAYU', 'PHONEPE', 'CASHFREE');

-- Create users table
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "password" TEXT NOT NULL,
    "role" "UserRole" NOT NULL DEFAULT 'ADMIN',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- Create email_accounts table
CREATE TABLE "email_accounts" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "type" "AccountType" NOT NULL DEFAULT 'STUDENT',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "quota" INTEGER NOT NULL DEFAULT **********,
    "usedQuota" INTEGER NOT NULL DEFAULT 0,
    "firstName" TEXT,
    "lastName" TEXT,
    "displayName" TEXT,
    "department" TEXT,
    "studentId" TEXT,
    "signature" TEXT,
    "autoReply" BOOLEAN NOT NULL DEFAULT false,
    "autoReplyMessage" TEXT,
    "forwardingEmail" TEXT,
    "lastLogin" TIMESTAMP(3),
    "loginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lockedUntil" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" TEXT,
    CONSTRAINT "email_accounts_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "email_accounts_email_key" ON "email_accounts"("email");

-- Create payment_catalog_items table
CREATE TABLE "payment_catalog_items" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "feeType" "FeeType" NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "category" TEXT,
    "subcategory" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isRecurring" BOOLEAN NOT NULL DEFAULT false,
    "recurringInterval" TEXT,
    "lateFee" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "discountPercentage" DECIMAL(5,2) NOT NULL DEFAULT 0,
    "dueDate" TIMESTAMP(3),
    "applicableFor" TEXT NOT NULL DEFAULT 'STUDENT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deletedAt" TIMESTAMP(3),
    "createdById" TEXT,
    CONSTRAINT "payment_catalog_items_pkey" PRIMARY KEY ("id")
);

-- Create payments table
CREATE TABLE "payments" (
    "id" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "catalogItemId" TEXT,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "feeType" "FeeType" NOT NULL,
    "description" TEXT NOT NULL,
    "gateway" "PaymentGateway" NOT NULL,
    "gatewayTxnId" TEXT,
    "gatewayResponse" TEXT,
    "additionalFee" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "status" "PaymentStatus" NOT NULL DEFAULT 'PENDING',
    "failureReason" TEXT,
    "receiptNumber" TEXT,
    "receiptUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "payments_transactionId_key" ON "payments"("transactionId");

-- Create settings table
CREATE TABLE "settings" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'STRING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "settings_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "settings_key_key" ON "settings"("key");

-- Add foreign key constraints
ALTER TABLE "email_accounts" ADD CONSTRAINT "email_accounts_createdById_fkey" 
    FOREIGN KEY ("createdById") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment_catalog_items" ADD CONSTRAINT "payment_catalog_items_createdById_fkey" 
    FOREIGN KEY ("createdById") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payments" ADD CONSTRAINT "payments_studentId_fkey" 
    FOREIGN KEY ("studentId") REFERENCES "email_accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "payments" ADD CONSTRAINT "payments_catalogItemId_fkey" 
    FOREIGN KEY ("catalogItemId") REFERENCES "payment_catalog_items"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create trigger function for updating updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON "users" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_accounts_updated_at BEFORE UPDATE ON "email_accounts" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_catalog_items_updated_at BEFORE UPDATE ON "payment_catalog_items" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON "payments" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON "settings" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial admin user
INSERT INTO "users" ("id", "email", "name", "password", "role", "createdAt", "updatedAt") 
VALUES (
    'admin-' || generate_random_uuid()::text,
    '<EMAIL>',
    'System Administrator',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJunKyJGy', -- Navsaharan89@
    'ADMIN',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT ("email") DO NOTHING;

-- Insert sample payment catalog items
INSERT INTO "payment_catalog_items" ("id", "name", "description", "feeType", "amount", "currency", "isActive", "applicableFor", "createdAt", "updatedAt") 
VALUES 
(
    'fee-' || generate_random_uuid()::text,
    'Semester Fee',
    'Regular semester tuition fee',
    'SEMESTER',
    15000.00,
    'INR',
    true,
    'STUDENT',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'fee-' || generate_random_uuid()::text,
    'Exam Fee',
    'End semester examination fee',
    'EXAM',
    2000.00,
    'INR',
    true,
    'STUDENT',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'fee-' || generate_random_uuid()::text,
    'Library Fee',
    'Annual library access fee',
    'LIBRARY',
    1000.00,
    'INR',
    true,
    'STUDENT',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT DO NOTHING;

-- Insert sample student email account
INSERT INTO "email_accounts" ("id", "email", "password", "type", "isActive", "displayName", "department", "studentId", "createdAt", "updatedAt") 
VALUES (
    'student-' || generate_random_uuid()::text,
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJunKyJGy', -- password123
    'STUDENT',
    true,
    'Student One',
    'Computer Science',
    'CS001',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT ("email") DO NOTHING;
