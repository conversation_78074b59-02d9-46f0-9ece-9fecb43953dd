{"buildCommand": "npm run build", "installCommand": "npm install", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}, "src/app/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=0, stale-while-revalidate"}]}], "rewrites": [{"source": "/admin/:path*", "destination": "/admin/:path*"}], "env": {"NEXT_PHASE": "phase-production-build", "SKIP_ENV_VALIDATION": "true"}, "build": {"env": {"NEXT_PHASE": "phase-production-build", "SKIP_ENV_VALIDATION": "true"}}, "regions": ["bom1"]}