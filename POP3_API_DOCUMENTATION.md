# 📧 POP3 API Simulation Documentation

## Overview

The POP3 API Simulation provides standard POP3 (Post Office Protocol version 3) compatibility for legacy email clients while operating over HTTP/HTTPS. This allows older email clients and applications that prefer POP3's download-and-delete model to access emails through our system using familiar POP3 commands and responses.

## 🚀 Quick Start

### Basic POP3 Session Flow

1. **Initialize Session**: `POST /api/pop3`
2. **Authenticate**: Send `USER` and `PASS` commands
3. **Access Messages**: Use `STAT`, `LIST`, `RETR` commands
4. **Manage Messages**: Use `DELE`, `RSET` commands
5. **Close Session**: Send `QUIT` command

### Example: Downloading Emails

```javascript
// 1. Initialize session
const initResponse = await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
})
const { sessionId } = await initResponse.json()

// 2. Send username
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'USER <EMAIL>'
  })
})

// 3. Send password
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'PASS password'
  })
})

// 4. Get mailbox statistics
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'STAT'
  })
})

// 5. List messages
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'LIST'
  })
})

// 6. Retrieve first message
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'RETR 1'
  })
})

// 7. Delete message
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'DELE 1'
  })
})

// 8. Quit and commit changes
await fetch('/api/pop3', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionId,
    command: 'QUIT'
  })
})
```

## 📡 API Endpoints

### 1. Main POP3 Endpoint

**`POST /api/pop3`** - POP3 Protocol Handler

**Request Body:**
```json
{
  "sessionId": "optional-session-id",
  "command": "POP3 command string"
}
```

**Response:**
```json
{
  "sessionId": "session-identifier",
  "response": {
    "status": "+OK|-ERR",
    "message": "response message",
    "data": ["optional", "multi-line", "data"]
  },
  "state": "AUTHORIZATION|TRANSACTION|UPDATE",
  "authenticated": true
}
```

**`GET /api/pop3`** - Service Information

Returns POP3 service capabilities and limits.

### 2. Configuration Endpoint

**`GET /api/pop3/config`** - Email Client Configuration

**Query Parameters:**
- `format`: `json|xml|autodiscover|thunderbird`
- `client`: `outlook|thunderbird|apple|android|ios|legacy`

**Examples:**
- `/api/pop3/config?client=outlook` - Outlook-specific settings
- `/api/pop3/config?format=xml` - Autodiscover XML
- `/api/pop3/config?client=legacy` - Legacy client settings

### 3. Testing Endpoint

**`POST /api/pop3/test`** - POP3 Protocol Testing

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

## 🔧 POP3 Commands Supported

### Authentication Commands
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `USER` | Send username | username | `USER <EMAIL>` |
| `PASS` | Send password | password | `PASS secretpassword` |

### Transaction Commands
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `STAT` | Get mailbox statistics | none | `STAT` |
| `LIST` | List messages | [msg_num] | `LIST` or `LIST 1` |
| `RETR` | Retrieve message | msg_num | `RETR 1` |
| `DELE` | Delete message | msg_num | `DELE 1` |
| `NOOP` | No operation | none | `NOOP` |
| `RSET` | Reset session | none | `RSET` |

### Optional Commands
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `UIDL` | Unique ID listing | [msg_num] | `UIDL` or `UIDL 1` |
| `TOP` | Message headers + lines | msg_num lines | `TOP 1 10` |
| `CAPA` | Server capabilities | none | `CAPA` |

### Session Commands
| Command | Description | Parameters | Example |
|---------|-------------|------------|---------|
| `QUIT` | End session | none | `QUIT` |

## 📋 POP3 Response Format

### Status Responses
| Status | Meaning | Description |
|--------|---------|-------------|
| +OK | Success | Command completed successfully |
| -ERR | Error | Command failed or invalid |

### Response Examples
```
+OK POP3 server ready
+OK User accepted
+OK 3 messages (1024 octets)
+OK Message follows
-ERR No such message
-ERR Authentication failed
```

## 🔄 POP3 Session States

### AUTHORIZATION State
- **Purpose**: User authentication
- **Available Commands**: USER, PASS, CAPA, QUIT
- **Transition**: Successful PASS → TRANSACTION

### TRANSACTION State
- **Purpose**: Message access and manipulation
- **Available Commands**: STAT, LIST, RETR, DELE, NOOP, RSET, UIDL, TOP, QUIT
- **Transition**: QUIT → UPDATE

### UPDATE State
- **Purpose**: Commit changes and cleanup
- **Available Commands**: None (automatic)
- **Transition**: Automatic → Session ends

## 🔐 Authentication

### USER/PASS Authentication
```
USER <EMAIL>
+OK User accepted
PASS password123
+OK 5 messages ready
```

### Security Features
- HTTPS encryption for all communications
- Session-based authentication
- Automatic session timeout (30 minutes)
- Rate limiting protection

## 📱 Email Client Configuration

### Microsoft Outlook

1. **Manual Setup:**
   - Account Type: POP3
   - Server: `your-domain.com/api/pop3`
   - Port: 995
   - Encryption: SSL/TLS
   - Authentication: Normal password

2. **Settings:**
   - Leave messages on server: Optional
   - Remove from server after: 14 days (recommended)

### Mozilla Thunderbird

1. **Manual Setup:**
   - Protocol: POP3
   - Hostname: `your-domain.com/api/pop3`
   - Port: 995
   - Security: SSL/TLS
   - Authentication: Normal password

### Apple Mail (iOS/macOS)

1. **Manual Setup:**
   - Account Type: POP
   - Server: `your-domain.com/api/pop3`
   - Port: 995
   - SSL: Yes
   - Authentication: Password

### Legacy Email Clients

1. **Basic Settings:**
   - Protocol: POP3
   - Server: `your-domain.com/api/pop3`
   - Port: 995 (SSL) or 110 (non-SSL)
   - Username: Full email address
   - Password: Account password

## 🛡️ Security Features

### Encryption
- All communications over HTTPS
- SSL/TLS support simulation
- Secure credential transmission

### Session Management
- 30-minute session timeout
- Secure session identifiers
- Automatic cleanup on QUIT

### Access Control
- User can only access their own emails
- Message-level security
- Deletion tracking and commit

### Rate Limiting
- Protection against brute force attacks
- Connection limits per IP
- Command rate limiting

## 📊 Limitations & Considerations

### POP3 Protocol Limitations
- Download-and-delete model
- Single folder access (INBOX only)
- No server-side search
- No folder management
- No message flags/labels

### Current Implementation Limitations
- HTTP-based simulation (not raw TCP POP3)
- Session storage in memory (use Redis for production)
- Basic authentication only
- Simplified message format

### Production Recommendations
- Use Redis for session storage
- Implement proper password hashing
- Add comprehensive logging
- Set up monitoring and alerts
- Configure proper rate limiting
- Consider IMAP for better functionality

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify username and password
   - Ensure account exists and is active
   - Check for typos in email address

2. **Session Expired**
   - Sessions timeout after 30 minutes
   - Create new session if expired
   - Use NOOP to keep session alive

3. **No Such Message**
   - Message number doesn't exist
   - Message may have been deleted
   - Use LIST to see available messages

4. **Command Not Valid**
   - Commands must be sent in correct state
   - Check current session state
   - Follow proper command sequence

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_POP3=true
```

## 📚 Additional Resources

- [RFC 1939 - Post Office Protocol Version 3](https://tools.ietf.org/html/rfc1939)
- [RFC 2449 - POP3 Extension Mechanism](https://tools.ietf.org/html/rfc2449)
- [RFC 1734 - POP3 AUTHentication command](https://tools.ietf.org/html/rfc1734)

## 🆘 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: `/api/pop3/config`
- Service Status: `/api/pop3` (GET)
- Testing: `/api/pop3/test` (Admin only)

## ⚠️ Important Notes

### POP3 vs IMAP
- **POP3**: Download-and-delete, single device access
- **IMAP**: Server-side storage, multi-device access
- **Recommendation**: Use IMAP for modern email clients

### Message Handling
- POP3 downloads messages to client
- Messages are removed from server after download
- Use "leave on server" option for multi-device access
- Consider backup strategies for important emails
