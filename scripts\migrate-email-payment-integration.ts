#!/usr/bin/env tsx

/**
 * Database Migration Script for Email & Payment Integration
 * 
 * This script handles the database migration for the SNPITC Email System
 * integration with the admin panel, including new tables and relations.
 */

import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

interface MigrationStep {
  name: string
  description: string
  execute: () => Promise<void>
  rollback?: () => Promise<void>
}

class DatabaseMigrator {
  private steps: MigrationStep[] = []
  private executedSteps: string[] = []

  constructor() {
    this.initializeMigrationSteps()
  }

  private initializeMigrationSteps() {
    this.steps = [
      {
        name: 'generate-prisma-client',
        description: 'Generate updated Prisma client with new schema',
        execute: async () => {
          console.log('🔄 Generating Prisma client...')
          execSync('npx prisma generate', { stdio: 'inherit' })
        }
      },
      {
        name: 'create-migration',
        description: 'Create database migration for email and payment integration',
        execute: async () => {
          console.log('🔄 Creating database migration...')
          execSync('npx prisma migrate dev --name email-payment-integration', { stdio: 'inherit' })
        }
      },
      {
        name: 'seed-payment-catalog',
        description: 'Seed initial payment catalog items',
        execute: async () => {
          console.log('🔄 Seeding payment catalog...')
          await this.seedPaymentCatalog()
        }
      },
      {
        name: 'create-admin-user',
        description: 'Ensure admin user exists for testing',
        execute: async () => {
          console.log('🔄 Creating admin user...')
          await this.createAdminUser()
        }
      },
      {
        name: 'setup-indexes',
        description: 'Create additional database indexes for performance',
        execute: async () => {
          console.log('🔄 Setting up database indexes...')
          await this.setupIndexes()
        }
      },
      {
        name: 'validate-schema',
        description: 'Validate database schema and relations',
        execute: async () => {
          console.log('🔄 Validating database schema...')
          await this.validateSchema()
        }
      }
    ]
  }

  private async seedPaymentCatalog() {
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!adminUser) {
      throw new Error('Admin user not found. Please create an admin user first.')
    }

    const catalogItems = [
      {
        name: 'Semester Fee',
        description: 'Regular semester tuition fee for undergraduate students',
        feeType: 'SEMESTER',
        amount: 50000,
        currency: 'INR',
        category: 'Academic',
        subcategory: 'Tuition',
        isActive: true,
        isRecurring: true,
        recurringInterval: 'SEMESTER',
        applicableFor: ['STUDENT'],
        createdById: adminUser.id
      },
      {
        name: 'Library Fee',
        description: 'Annual library access and book borrowing fee',
        feeType: 'LIBRARY',
        amount: 2000,
        currency: 'INR',
        category: 'Services',
        subcategory: 'Library',
        isActive: true,
        isRecurring: true,
        recurringInterval: 'YEARLY',
        applicableFor: ['STUDENT', 'FACULTY'],
        createdById: adminUser.id
      },
      {
        name: 'Exam Fee',
        description: 'Semester examination fee',
        feeType: 'EXAM',
        amount: 1500,
        currency: 'INR',
        category: 'Academic',
        subcategory: 'Examination',
        isActive: true,
        isRecurring: true,
        recurringInterval: 'SEMESTER',
        applicableFor: ['STUDENT'],
        createdById: adminUser.id
      },
      {
        name: 'Hostel Fee',
        description: 'Monthly hostel accommodation fee',
        feeType: 'HOSTEL',
        amount: 8000,
        currency: 'INR',
        category: 'Accommodation',
        subcategory: 'Hostel',
        isActive: true,
        isRecurring: true,
        recurringInterval: 'MONTHLY',
        applicableFor: ['STUDENT'],
        createdById: adminUser.id
      },
      {
        name: 'Transport Fee',
        description: 'Monthly bus transportation fee',
        feeType: 'TRANSPORT',
        amount: 1200,
        currency: 'INR',
        category: 'Services',
        subcategory: 'Transportation',
        isActive: true,
        isRecurring: true,
        recurringInterval: 'MONTHLY',
        applicableFor: ['STUDENT', 'FACULTY', 'STAFF'],
        createdById: adminUser.id
      }
    ]

    for (const item of catalogItems) {
      const existing = await prisma.paymentCatalogItem.findFirst({
        where: { name: item.name }
      })

      if (!existing) {
        await prisma.paymentCatalogItem.create({
          data: item as any
        })
        console.log(`✅ Created payment catalog item: ${item.name}`)
      } else {
        console.log(`⏭️  Payment catalog item already exists: ${item.name}`)
      }
    }
  }

  private async createAdminUser() {
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!existingAdmin) {
      const adminUser = await prisma.user.create({
        data: {
          name: 'System Administrator',
          email: '<EMAIL>',
          role: 'ADMIN',
          emailVerified: new Date()
        }
      })
      console.log(`✅ Created admin user: ${adminUser.email}`)
    } else {
      console.log(`⏭️  Admin user already exists: ${existingAdmin.email}`)
    }
  }

  private async setupIndexes() {
    // These indexes will be created by Prisma migrations, but we can add custom ones here
    const customIndexes = [
      // Email performance indexes
      'CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_emails_from_email ON emails(from_email);',
      'CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status);',
      'CREATE INDEX IF NOT EXISTS idx_emails_spam ON emails(is_spam);',
      
      // Payment performance indexes
      'CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);',
      'CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);',
      'CREATE INDEX IF NOT EXISTS idx_payments_gateway ON payments(gateway);',
      
      // Audit log indexes
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON admin_audit_logs(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON admin_audit_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON admin_audit_logs(action);',
      
      // Alert indexes
      'CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON system_alerts(created_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_alerts_acknowledged ON system_alerts(is_acknowledged);'
    ]

    for (const indexSql of customIndexes) {
      try {
        await prisma.$executeRawUnsafe(indexSql)
        console.log(`✅ Created index: ${indexSql.split(' ')[5]}`)
      } catch (error) {
        console.log(`⏭️  Index already exists or error: ${indexSql.split(' ')[5]}`)
      }
    }
  }

  private async validateSchema() {
    try {
      // Test basic queries to ensure schema is working
      const userCount = await prisma.user.count()
      const emailAccountCount = await prisma.emailAccount.count()
      const paymentCatalogCount = await prisma.paymentCatalogItem.count()
      
      console.log(`✅ Schema validation successful:`)
      console.log(`   - Users: ${userCount}`)
      console.log(`   - Email Accounts: ${emailAccountCount}`)
      console.log(`   - Payment Catalog Items: ${paymentCatalogCount}`)

      // Test relations
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' },
        include: {
          createdPaymentItems: true,
          adminActions: true
        }
      })

      if (adminUser) {
        console.log(`✅ Relations working: Admin has ${adminUser.createdPaymentItems.length} payment items`)
      }

    } catch (error) {
      throw new Error(`Schema validation failed: ${error}`)
    }
  }

  async runMigration() {
    console.log('🚀 Starting Email & Payment Integration Migration...\n')

    try {
      for (const step of this.steps) {
        console.log(`📋 Step: ${step.description}`)
        await step.execute()
        this.executedSteps.push(step.name)
        console.log(`✅ Completed: ${step.name}\n`)
      }

      console.log('🎉 Migration completed successfully!')
      console.log('\n📊 Migration Summary:')
      console.log(`   - Executed ${this.executedSteps.length} steps`)
      console.log(`   - Database schema updated`)
      console.log(`   - Payment catalog seeded`)
      console.log(`   - Indexes created`)
      console.log(`   - Schema validated`)

    } catch (error) {
      console.error('❌ Migration failed:', error)
      console.log('\n🔄 Executed steps:', this.executedSteps)
      throw error
    } finally {
      await prisma.$disconnect()
    }
  }

  async rollback() {
    console.log('🔄 Rolling back migration...')
    
    try {
      // Note: This is a simplified rollback. In production, you'd want more sophisticated rollback logic
      console.log('⚠️  Manual rollback required. Please run: npx prisma migrate reset')
    } catch (error) {
      console.error('❌ Rollback failed:', error)
    } finally {
      await prisma.$disconnect()
    }
  }
}

// Main execution
async function main() {
  const migrator = new DatabaseMigrator()
  
  const args = process.argv.slice(2)
  const command = args[0]

  switch (command) {
    case 'rollback':
      await migrator.rollback()
      break
    case 'migrate':
    default:
      await migrator.runMigration()
      break
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('Migration script failed:', error)
    process.exit(1)
  })
}

export { DatabaseMigrator }
