import { PrismaClient } from '@prisma/client'
import fs from 'fs'

const prisma = new PrismaClient()

async function simpleExport() {
  try {
    console.log('🔄 Exporting critical data...')

    // Export users (admin accounts)
    const users = await prisma.user.findMany()
    console.log(`👥 Found ${users.length} users`)

    // Export email accounts (student accounts)
    const emailAccounts = await prisma.emailAccount.findMany()
    console.log(`📧 Found ${emailAccounts.length} email accounts`)

    // Export payment catalog items
    const paymentItems = await prisma.paymentCatalogItem.findMany()
    console.log(`💰 Found ${paymentItems.length} payment items`)

    // Export payments
    const payments = await prisma.payment.findMany()
    console.log(`💳 Found ${payments.length} payments`)

    // Export settings (if table exists)
    let settings = []
    try {
      settings = await prisma.settings.findMany()
      console.log(`⚙️ Found ${settings.length} settings`)
    } catch (error) {
      console.log('⚠️ Settings table not found, skipping...')
    }

    const exportData = {
      users,
      emailAccounts,
      paymentItems,
      payments,
      settings,
      exportedAt: new Date().toISOString()
    }

    // Convert BigInt to string for JSON serialization
    const jsonData = JSON.stringify(exportData, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value, 2)

    fs.writeFileSync('data-backup.json', jsonData)
    console.log('✅ Data exported to data-backup.json')

    return exportData
  } catch (error) {
    console.error('❌ Export failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

simpleExport()
  .then(() => console.log('🎉 Export completed!'))
  .catch(console.error)
