{"name": "snpitc-remake", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "build:prod": "prisma generate && NODE_ENV=production next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "postinstall": "prisma generate", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "migrate:supabase": "tsx scripts/migrate-to-supabase.ts", "analyze": "ANALYZE=true npm run build", "vercel-build": "prisma generate && next build", "docker:build": "docker build -t snpitc-email .", "docker:run": "docker run -p 3000:3000 snpitc-email", "deploy:prod": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "backup": "docker-compose run --rm backup", "logs": "docker-compose logs -f", "health": "curl -f http://localhost:3000/api/health", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/api tests/components --passWithNoTests", "test:integration": "jest tests/integration --passWithNoTests", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "tsx scripts/run-tests.ts", "migrate:email-payment": "tsx scripts/migrate-email-payment-integration.ts", "migrate:rollback": "tsx scripts/migrate-email-payment-integration.ts rollback", "migrate:validate": "tsx scripts/validate-migration.ts", "env:validate": "tsx scripts/validate-env.ts", "deploy:check": "npm run env:validate && npm run build"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/storage-js": "^2.5.5", "@supabase/supabase-js": "^2.39.0", "@tiptap/extension-bullet-list": "^2.14.0", "@tiptap/extension-character-count": "^2.14.1", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-font-family": "^2.22.0", "@tiptap/extension-hard-break": "^2.14.1", "@tiptap/extension-highlight": "^2.22.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-ordered-list": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.1", "@tiptap/extension-subscript": "^2.22.0", "@tiptap/extension-superscript": "^2.22.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.22.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.517.0", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "pg": "^8.16.3", "prisma": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "uuid": "^10.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-mocks-http": "^1.13.0", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}