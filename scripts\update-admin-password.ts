import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function updateAdminPassword() {
  try {
    console.log('🔐 Updating admin password...')

    // New password
    const newPassword = 'Navsaharan89@'
    
    // Hash the new password
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds)
    
    console.log('✅ Password hashed successfully')

    // Find admin user
    const adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'ADMIN'
      }
    })

    if (!adminUser) {
      console.log('❌ Admin user not found, creating new admin user...')
      
      // Create new admin user
      const newAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'System Administrator',
          password: hashedPassword,
          role: 'ADMIN'
        }
      })

      console.log('✅ New admin user created:', {
        id: newAdmin.id,
        email: newAdmin.email,
        role: newAdmin.role
      })

    } else {
      console.log('👤 Found existing admin user:', {
        id: adminUser.id,
        email: adminUser.email,
        role: adminUser.role
      })

      // Update existing admin password
      const updatedAdmin = await prisma.user.update({
        where: {
          id: adminUser.id
        },
        data: {
          password: hashedPassword
        }
      })

      console.log('✅ Admin password updated successfully')
    }

    // Verify the password works
    const verifyUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    })

    if (verifyUser) {
      const isPasswordValid = await bcrypt.compare(newPassword, verifyUser.password)
      
      if (isPasswordValid) {
        console.log('✅ Password verification successful')
        console.log('🎉 Admin credentials updated successfully!')
        console.log('')
        console.log('📋 New Admin Credentials:')
        console.log('   Email: <EMAIL>')
        console.log('   Password: Navsaharan89@')
        console.log('')
        console.log('⚠️  Please keep these credentials secure!')
      } else {
        console.log('❌ Password verification failed')
      }
    }

  } catch (error) {
    console.error('❌ Error updating admin password:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
updateAdminPassword()
  .then(() => {
    console.log('✅ Script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
