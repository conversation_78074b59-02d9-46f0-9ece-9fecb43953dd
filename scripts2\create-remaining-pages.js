const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const facilitiesPages = [
  {
    title: 'Infrastructure, Building and Workshop',
    slug: 'infrastructure',
    description: 'Complete details of infrastructure, building area, workshop facilities, and other physical amenities at the institute.',
    content: `
      <h2>Infrastructure, Building and Workshop</h2>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Sl. No.</th>
            <th>Name</th>
            <th>Area in Sq. Mt.</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>a</td>
            <td>Total Area</td>
            <td>557 Sq. Mt.</td>
            <td>Complete campus area</td>
          </tr>
          <tr>
            <td>b</td>
            <td>Covered Area</td>
            <td>457 Sq. Mt.</td>
            <td>Built-up area including all buildings</td>
          </tr>
          <tr>
            <td>c</td>
            <td>Total Class Room Area</td>
            <td>42 Sq. Mt.</td>
            <td>Dedicated classroom space for theory classes</td>
          </tr>
          <tr>
            <td>d</td>
            <td>Total Workshop Area</td>
            <td>200 Sq. Mt.</td>
            <td>Practical training workshop area</td>
          </tr>
          <tr>
            <td>e</td>
            <td>Drawing Hall</td>
            <td>Included in trade workshop</td>
            <td>Technical drawing and drafting area</td>
          </tr>
          <tr>
            <td>f</td>
            <td>Audio Visual Hall</td>
            <td>-</td>
            <td>Multimedia presentation facility</td>
          </tr>
          <tr>
            <td>g</td>
            <td>Computer Lab</td>
            <td>380 Sq. Mt.</td>
            <td>Modern computer laboratory</td>
          </tr>
          <tr>
            <td>h</td>
            <td>Play Area</td>
            <td>-</td>
            <td>Sports and recreational area</td>
          </tr>
          <tr>
            <td>i</td>
            <td>Library</td>
            <td>27.5 Sq. Mt.</td>
            <td>Well-equipped library with technical books</td>
          </tr>
        </tbody>
      </table>
      
      <h3>Key Features:</h3>
      <ul>
        <li>Modern workshop facilities with latest equipment</li>
        <li>Well-ventilated classrooms for comfortable learning</li>
        <li>Dedicated computer lab for digital literacy</li>
        <li>Library with technical and general books</li>
        <li>Audio-visual facilities for enhanced learning</li>
        <li>Adequate space for practical training</li>
      </ul>
    `,
    metaTitle: 'Infrastructure and Facilities - S.N. Pvt. ITI',
    metaDesc: 'Complete infrastructure details of S.N. Pvt. ITI including building area, workshop facilities, computer lab, library, and other amenities.',
    status: 'PUBLISHED'
  },
  {
    title: 'Trade Specific Infrastructure',
    slug: 'ts-infrastructure',
    description: 'Detailed information about trade-specific infrastructure and equipment for Electrician trade.',
    content: `
      <h2>Trade Specific Infrastructure</h2>
      <h3>Electrician Trade Infrastructure</h3>
      
      <p>S.N. Pvt. Industrial Training Institute has well-equipped infrastructure specifically designed for Electrician trade training.</p>
      
      <h4>Workshop Equipment:</h4>
      <ul>
        <li>Electrical wiring boards and panels</li>
        <li>Motor control circuits</li>
        <li>Power distribution systems</li>
        <li>Measuring instruments and multimeters</li>
        <li>Hand tools and power tools</li>
        <li>Safety equipment and protective gear</li>
        <li>Electrical components and materials</li>
        <li>Testing and troubleshooting equipment</li>
      </ul>
      
      <h4>Training Facilities:</h4>
      <ul>
        <li>Dedicated electrician workshop area</li>
        <li>Individual workbenches for students</li>
        <li>Demonstration boards for practical learning</li>
        <li>Safety training area</li>
        <li>Tool storage and maintenance facility</li>
      </ul>
      
      <h4>Safety Measures:</h4>
      <ul>
        <li>Fire safety equipment</li>
        <li>First aid facilities</li>
        <li>Emergency shutdown systems</li>
        <li>Proper ventilation and lighting</li>
        <li>Safety protocols and guidelines</li>
      </ul>
      
      <p>All equipment and infrastructure meet NCVT standards and are regularly maintained to ensure optimal learning conditions.</p>
    `,
    metaTitle: 'Trade Specific Infrastructure - S.N. Pvt. ITI',
    metaDesc: 'Trade-specific infrastructure and equipment for Electrician trade at S.N. Pvt. ITI including workshop facilities and safety measures.',
    status: 'PUBLISHED'
  },
  {
    title: 'Electric Power Supply',
    slug: 'electric-power',
    description: 'Information about electric power supply arrangements and backup facilities at the institute.',
    content: `
      <h2>Electric Power Supply</h2>
      
      <h3>Power Supply Arrangements</h3>
      <p>S.N. Pvt. Industrial Training Institute has reliable power supply arrangements to ensure uninterrupted training and operations.</p>
      
      <h4>Main Power Supply:</h4>
      <ul>
        <li>Connected to state electricity grid</li>
        <li>Adequate load capacity for all operations</li>
        <li>Proper electrical distribution system</li>
        <li>Safety switches and circuit breakers</li>
      </ul>
      
      <h4>Backup Power:</h4>
      <ul>
        <li>Generator backup for essential operations</li>
        <li>UPS systems for computer lab and office</li>
        <li>Emergency lighting systems</li>
        <li>Battery backup for critical equipment</li>
      </ul>
      
      <h4>Safety Features:</h4>
      <ul>
        <li>Earthing and grounding systems</li>
        <li>Circuit protection devices</li>
        <li>Emergency shutdown procedures</li>
        <li>Regular electrical safety inspections</li>
        <li>Fire safety measures</li>
      </ul>
      
      <h4>Energy Conservation:</h4>
      <ul>
        <li>LED lighting throughout the campus</li>
        <li>Energy-efficient equipment</li>
        <li>Power factor correction</li>
        <li>Regular energy audits</li>
      </ul>
      
      <p>The institute maintains high standards of electrical safety and ensures reliable power supply for all training activities.</p>
    `,
    metaTitle: 'Electric Power Supply - S.N. Pvt. ITI',
    metaDesc: 'Electric power supply arrangements, backup facilities, and safety measures at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Library',
    slug: 'library',
    description: 'Information about library facilities, book collection, and reading resources available at the institute.',
    content: `
      <h2>Library</h2>
      
      <h3>Library Facilities</h3>
      <p>S.N. Pvt. Industrial Training Institute has a well-equipped library with a comprehensive collection of technical and general books.</p>
      
      <h4>Library Details:</h4>
      <ul>
        <li><strong>Area:</strong> 27.5 Sq. Mt.</li>
        <li><strong>Seating Capacity:</strong> 20 students</li>
        <li><strong>Reading Hours:</strong> 9:00 AM to 5:00 PM</li>
        <li><strong>Location:</strong> Ground floor of main building</li>
      </ul>
      
      <h4>Book Collection:</h4>
      <ul>
        <li>Technical books related to Electrician trade</li>
        <li>Reference books and manuals</li>
        <li>General knowledge and competitive exam books</li>
        <li>Magazines and technical journals</li>
        <li>NCVT syllabus and examination materials</li>
        <li>Safety and industrial training resources</li>
      </ul>
      
      <h4>Services:</h4>
      <ul>
        <li>Book lending facility for students</li>
        <li>Reading room with comfortable seating</li>
        <li>Reference section for quick consultation</li>
        <li>Newspaper and magazine section</li>
        <li>Study material for examinations</li>
      </ul>
      
      <h4>Library Rules:</h4>
      <ul>
        <li>Students must maintain silence in the library</li>
        <li>Books can be issued for 15 days</li>
        <li>Fine for late return of books</li>
        <li>Proper care of books and library property</li>
        <li>No food or drinks allowed in the library</li>
      </ul>
      
      <p>The library plays a crucial role in supporting the academic and technical development of students.</p>
    `,
    metaTitle: 'Library Facilities - S.N. Pvt. ITI',
    metaDesc: 'Library facilities, book collection, and reading resources available at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  }
]

async function createFacilitiesPages() {
  try {
    console.log('🚀 Starting facilities pages creation...')
    
    for (const pageData of facilitiesPages) {
      console.log(`Creating page: ${pageData.title}`)
      
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })
      
      if (existingPage) {
        console.log(`⚠️  Page ${pageData.slug} already exists, skipping...`)
        continue
      }
      
      // Create the page
      await prisma.page.create({
        data: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          content: pageData.content,
          metaTitle: pageData.metaTitle,
          metaDesc: pageData.metaDesc,
          status: pageData.status,
          order: 0,
          createdById: 'cmc1p577i0000fcy4iafh42k2' // <EMAIL>
        }
      })
      
      console.log(`✅ Created: ${pageData.title}`)
    }
    
    console.log('🎉 Facilities pages creation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error creating pages:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createFacilitiesPages()
