@echo off
setlocal enabledelayedexpansion

REM SNPITC Website Deployment Setup Script (Windows)
REM This script helps automate the deployment preparation process

echo.
echo 🚀 SNPITC Website Deployment Setup
echo ==================================
echo.

REM Check if Node.js is installed
echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js is installed: !NODE_VERSION!
)

REM Check if npm is installed
echo Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm is installed: !NPM_VERSION!
)

echo.
echo ℹ️ Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully

echo.
echo ℹ️ Setting up Prisma...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Failed to generate Prisma client
    pause
    exit /b 1
)
echo ✅ Prisma client generated

echo.
echo ℹ️ Creating environment file template...
if not exist .env.local (
    (
        echo # Database
        echo DATABASE_URL="file:./dev.db"
        echo.
        echo # NextAuth.js
        echo NEXTAUTH_SECRET="your-super-secure-secret-key-change-this-to-a-secure-random-string"
        echo NEXTAUTH_URL="http://localhost:3000"
        echo.
        echo # Admin credentials
        echo ADMIN_EMAIL="<EMAIL>"
        echo ADMIN_PASSWORD="change-this-secure-password"
        echo.
        echo # Site configuration
        echo SITE_NAME="S.N. Pvt. Industrial Training Institute"
        echo SITE_URL="http://localhost:3000"
        echo.
        echo # Email configuration ^(optional^)
        echo SMTP_HOST=""
        echo SMTP_PORT="587"
        echo SMTP_USER=""
        echo SMTP_PASS=""
    ) > .env.local
    echo ✅ Environment file created: .env.local
    echo ⚠️ Please update the environment variables in .env.local
) else (
    echo ℹ️ .env.local already exists, skipping creation
)

echo.
echo ℹ️ Setting up database...
if not exist prisma\dev.db (
    echo ℹ️ Creating database and running migrations...
    call npx prisma db push
    if %errorlevel% neq 0 (
        echo ❌ Failed to create database
        pause
        exit /b 1
    )
    echo ✅ Database created and schema applied
    
    echo ℹ️ Seeding initial data...
    call npm run db:seed
    if %errorlevel% neq 0 (
        echo ⚠️ Failed to seed database, but continuing...
    ) else (
        echo ✅ Database seeded with initial data
    )
) else (
    echo ⚠️ Database already exists. Skipping initial setup.
)

echo.
echo ℹ️ Creating production environment template...
if not exist .env.production.template (
    (
        echo # Production Environment Template
        echo # Copy this to .env.production and update with your production values
        echo.
        echo # Database ^(Update with your production database URL^)
        echo DATABASE_URL="****************************************/database?sslmode=require"
        echo.
        echo # NextAuth.js ^(Generate a secure secret^)
        echo NEXTAUTH_SECRET="generate-a-secure-32-character-secret-key"
        echo NEXTAUTH_URL="https://your-domain.com"
        echo.
        echo # Admin credentials ^(Use secure passwords^)
        echo ADMIN_EMAIL="<EMAIL>"
        echo ADMIN_PASSWORD="secure-production-password"
        echo.
        echo # Site configuration
        echo SITE_NAME="S.N. Pvt. Industrial Training Institute"
        echo SITE_URL="https://your-domain.com"
        echo.
        echo # Email configuration ^(Configure for production^)
        echo SMTP_HOST="your-smtp-host"
        echo SMTP_PORT="587"
        echo SMTP_USER="your-smtp-user"
        echo SMTP_PASS="your-smtp-password"
        echo.
        echo # Production settings
        echo NODE_ENV="production"
    ) > .env.production.template
    echo ✅ Production environment template created
)

echo.
echo ℹ️ Building the project...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)
echo ✅ Project built successfully

echo.
echo ℹ️ Running type check...
call npm run type-check
if %errorlevel% neq 0 (
    echo ⚠️ Type check failed, but continuing...
) else (
    echo ✅ Type check passed
)

echo.
echo ℹ️ Running linter...
call npm run lint
if %errorlevel% neq 0 (
    echo ⚠️ Linting failed, but continuing...
) else (
    echo ✅ Linting passed
)

echo.
echo ✅ 🎉 Deployment setup completed successfully!
echo.
echo ℹ️ Next steps:
echo 1. Update environment variables in .env.local
echo 2. Test the application locally: npm run dev
echo 3. Choose a hosting platform from HOSTING_DEPLOYMENT.md
echo 4. Follow the deployment guide for your chosen platform
echo.
echo ℹ️ For detailed deployment instructions, see: HOSTING_DEPLOYMENT.md
echo.
echo ⚠️ Please generate a secure secret for NEXTAUTH_SECRET in production
echo You can use online tools or PowerShell to generate a secure random string
echo.
pause
