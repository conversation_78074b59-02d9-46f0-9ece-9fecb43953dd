#!/usr/bin/env node

/**
 * Health Check Script for Production Deployment
 * 
 * Performs comprehensive health checks including:
 * - Application responsiveness
 * - Database connectivity
 * - Redis connectivity
 * - Critical service availability
 */

const http = require('http')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function healthCheck() {
  const checks = []
  let allHealthy = true

  // 1. Check application responsiveness
  try {
    await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: 'localhost',
        port: 3000,
        path: '/api/health',
        method: 'GET',
        timeout: 5000
      }, (res) => {
        if (res.statusCode === 200) {
          resolve()
        } else {
          reject(new Error(`HTTP ${res.statusCode}`))
        }
      })
      
      req.on('error', reject)
      req.on('timeout', () => reject(new Error('Request timeout')))
      req.end()
    })
    
    checks.push({ service: 'Application', status: 'healthy' })
  } catch (error) {
    checks.push({ service: 'Application', status: 'unhealthy', error: error.message })
    allHealthy = false
  }

  // 2. Check database connectivity
  try {
    await prisma.$queryRaw`SELECT 1`
    checks.push({ service: 'Database', status: 'healthy' })
  } catch (error) {
    checks.push({ service: 'Database', status: 'unhealthy', error: error.message })
    allHealthy = false
  }

  // 3. Check Redis connectivity (if configured)
  if (process.env.REDIS_URL) {
    try {
      const redis = require('redis')
      const client = redis.createClient({ url: process.env.REDIS_URL })
      await client.connect()
      await client.ping()
      await client.disconnect()
      checks.push({ service: 'Redis', status: 'healthy' })
    } catch (error) {
      checks.push({ service: 'Redis', status: 'unhealthy', error: error.message })
      allHealthy = false
    }
  }

  // 4. Check critical email service
  try {
    const emailAccountCount = await prisma.emailAccount.count()
    if (emailAccountCount >= 0) {
      checks.push({ service: 'Email Service', status: 'healthy' })
    } else {
      throw new Error('Email service not accessible')
    }
  } catch (error) {
    checks.push({ service: 'Email Service', status: 'unhealthy', error: error.message })
    allHealthy = false
  }

  // 5. Check payment service
  try {
    const paymentItemCount = await prisma.paymentCatalogItem.count()
    if (paymentItemCount >= 0) {
      checks.push({ service: 'Payment Service', status: 'healthy' })
    } else {
      throw new Error('Payment service not accessible')
    }
  } catch (error) {
    checks.push({ service: 'Payment Service', status: 'unhealthy', error: error.message })
    allHealthy = false
  }

  // Cleanup
  await prisma.$disconnect()

  // Output results
  const timestamp = new Date().toISOString()
  const result = {
    timestamp,
    status: allHealthy ? 'healthy' : 'unhealthy',
    checks
  }

  console.log(JSON.stringify(result, null, 2))

  // Exit with appropriate code
  process.exit(allHealthy ? 0 : 1)
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('Health check failed:', error)
  process.exit(1)
})

process.on('unhandledRejection', (error) => {
  console.error('Health check failed:', error)
  process.exit(1)
})

// Run health check
healthCheck().catch((error) => {
  console.error('Health check failed:', error)
  process.exit(1)
})
