import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

interface ExportedData {
  users: any[]
  emailAccounts: any[]
  emails: any[]
  emailRecipients: any[]
  emailAttachments: any[]
  emailFolders: any[]
  payments: any[]
  paymentCatalogItems: any[]
  paymentGatewayConfigs: any[]
  settings: any[]
  exportMetadata: {
    exportedAt: string
    totalRecords: number
    version: string
  }
}

async function exportSQLiteData(): Promise<void> {
  try {
    console.log('🔄 Starting SQLite data export...')

    // Export all data with relationships
    console.log('📊 Exporting users...')
    const users = await prisma.user.findMany()

    console.log('📧 Exporting email accounts...')
    const emailAccounts = await prisma.emailAccount.findMany()

    console.log('✉️ Exporting emails...')
    const emails = await prisma.email.findMany()

    console.log('👥 Exporting email recipients...')
    const emailRecipients = await prisma.emailRecipient.findMany()

    console.log('📎 Exporting email attachments...')
    const emailAttachments = await prisma.emailAttachment.findMany()

    console.log('📁 Exporting email folders...')
    const emailFolders = await prisma.emailFolder.findMany()

    console.log('💳 Exporting payments...')
    const payments = await prisma.payment.findMany()

    console.log('🏷️ Exporting payment catalog items...')
    const paymentCatalogItems = await prisma.paymentCatalogItem.findMany()

    console.log('⚙️ Exporting payment gateway configs...')
    const paymentGatewayConfigs = await prisma.paymentGatewayConfig.findMany()

    console.log('🔧 Exporting settings...')
    const settings = await prisma.settings.findMany()

    // Calculate total records
    const totalRecords = users.length + emailAccounts.length + emails.length + 
                        emailRecipients.length + emailAttachments.length + 
                        emailFolders.length + payments.length + 
                        paymentCatalogItems.length + paymentGatewayConfigs.length + 
                        settings.length

    // Prepare export data
    const exportData: ExportedData = {
      users,
      emailAccounts,
      emails,
      emailRecipients,
      emailAttachments,
      emailFolders,
      payments,
      paymentCatalogItems,
      paymentGatewayConfigs,
      settings,
      exportMetadata: {
        exportedAt: new Date().toISOString(),
        totalRecords,
        version: '1.0.0'
      }
    }

    // Create exports directory if it doesn't exist
    const exportsDir = path.join(process.cwd(), 'exports')
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true })
    }

    // Write export file
    const exportFileName = `sqlite-export-${new Date().toISOString().split('T')[0]}.json`
    const exportPath = path.join(exportsDir, exportFileName)
    
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2))

    // Create a summary report
    const summaryReport = {
      exportDate: new Date().toISOString(),
      fileName: exportFileName,
      filePath: exportPath,
      fileSize: `${(fs.statSync(exportPath).size / 1024).toFixed(2)} KB`,
      recordCounts: {
        users: users.length,
        emailAccounts: emailAccounts.length,
        emails: emails.length,
        emailRecipients: emailRecipients.length,
        emailAttachments: emailAttachments.length,
        emailFolders: emailFolders.length,
        payments: payments.length,
        paymentCatalogItems: paymentCatalogItems.length,
        paymentGatewayConfigs: paymentGatewayConfigs.length,
        settings: settings.length,
        total: totalRecords
      }
    }

    // Write summary report
    const summaryPath = path.join(exportsDir, 'export-summary.json')
    fs.writeFileSync(summaryPath, JSON.stringify(summaryReport, null, 2))

    console.log('\n✅ SQLite data export completed successfully!')
    console.log(`📁 Export file: ${exportPath}`)
    console.log(`📊 Summary file: ${summaryPath}`)
    console.log(`📈 Total records exported: ${totalRecords}`)
    console.log('\n📋 Record breakdown:')
    Object.entries(summaryReport.recordCounts).forEach(([table, count]) => {
      if (table !== 'total') {
        console.log(`   ${table}: ${count} records`)
      }
    })

    console.log('\n🎯 Next steps:')
    console.log('1. Set up Supabase project')
    console.log('2. Update Prisma schema for PostgreSQL')
    console.log('3. Run migration script to import data')
    console.log('4. Test the application with new database')

  } catch (error) {
    console.error('❌ Error during SQLite data export:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the export
if (require.main === module) {
  exportSQLiteData()
    .then(() => {
      console.log('\n🎉 Export process completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Export process failed:', error)
      process.exit(1)
    })
}

export { exportSQLiteData }
