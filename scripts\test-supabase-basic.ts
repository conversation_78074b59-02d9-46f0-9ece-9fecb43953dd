import { PrismaClient } from '@prisma/client'

async function testBasicOperations() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔌 Testing basic Supabase operations...')
    
    // Test connection
    await prisma.$connect()
    console.log('✅ Connected to Supabase')
    
    // Test if we can query users table
    try {
      const userCount = await prisma.user.count()
      console.log(`✅ Users table accessible, found ${userCount} users`)
    } catch (error) {
      console.log('⚠️ Users table not accessible:', error.message)
    }
    
    // Test if we can query email accounts table
    try {
      const accountCount = await prisma.emailAccount.count()
      console.log(`✅ Email accounts table accessible, found ${accountCount} accounts`)
    } catch (error) {
      console.log('⚠️ Email accounts table not accessible:', error.message)
    }
    
    // Test if we can query payment catalog items table
    try {
      const itemCount = await prisma.paymentCatalogItem.count()
      console.log(`✅ Payment catalog items table accessible, found ${itemCount} items`)
    } catch (error) {
      console.log('⚠️ Payment catalog items table not accessible:', error.message)
    }
    
    console.log('🎉 Basic operations test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

testBasicOperations()
  .then(() => {
    console.log('✅ All tests passed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Tests failed:', error.message)
    process.exit(1)
  })
