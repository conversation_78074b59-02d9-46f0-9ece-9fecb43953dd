import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

interface BackupData {
  users: any[]
  emailAccounts: any[]
  paymentItems: any[]
  payments: any[]
  settings: any[]
}

async function setupCloudDatabase() {
  try {
    console.log('🚀 Setting up cloud database...')

    // Test database connection
    console.log('🔌 Testing database connection...')
    await prisma.$connect()
    console.log('✅ Database connection successful!')

    // Generate and apply migrations
    console.log('📋 Generating Prisma client...')
    // Note: In production, run `npx prisma migrate deploy` instead of db push
    
    console.log('🔄 Setting up database schema...')
    // This will create tables if they don't exist
    await prisma.$executeRaw`SELECT 1`
    console.log('✅ Database schema ready!')

    // Check if we have backup data to import
    if (fs.existsSync('data-backup.json')) {
      console.log('📦 Found backup data, importing...')
      await importBackupData()
    } else {
      console.log('🆕 No backup data found, creating initial data...')
      await createInitialData()
    }

    console.log('🎉 Cloud database setup completed successfully!')
    
  } catch (error) {
    console.error('❌ Database setup failed:', error)
    
    if (error.code === 'P1001') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('1. Your DATABASE_URL is correct')
      console.log('2. Your database server is running')
      console.log('3. Your network allows connections to the database')
      console.log('4. Your database credentials are valid')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function importBackupData() {
  try {
    const backupData: BackupData = JSON.parse(fs.readFileSync('data-backup.json', 'utf8'))
    
    console.log('👥 Importing users...')
    for (const user of backupData.users) {
      await prisma.user.upsert({
        where: { email: user.email },
        update: {
          name: user.name,
          password: user.password,
          role: user.role,
          updatedAt: new Date()
        },
        create: {
          email: user.email,
          name: user.name,
          password: user.password,
          role: user.role,
          createdAt: new Date(user.createdAt),
          updatedAt: new Date(user.updatedAt)
        }
      })
    }
    console.log(`✅ Imported ${backupData.users.length} users`)

    console.log('📧 Importing email accounts...')
    for (const account of backupData.emailAccounts) {
      await prisma.emailAccount.upsert({
        where: { email: account.email },
        update: {
          password: account.password,
          type: account.type,
          isActive: account.isActive,
          quota: BigInt(account.quota),
          usedQuota: BigInt(account.usedQuota),
          firstName: account.firstName,
          lastName: account.lastName,
          displayName: account.displayName,
          department: account.department,
          studentId: account.studentId,
          updatedAt: new Date()
        },
        create: {
          email: account.email,
          password: account.password,
          type: account.type,
          isActive: account.isActive,
          quota: BigInt(account.quota),
          usedQuota: BigInt(account.usedQuota),
          firstName: account.firstName,
          lastName: account.lastName,
          displayName: account.displayName,
          department: account.department,
          studentId: account.studentId,
          createdAt: new Date(account.createdAt),
          updatedAt: new Date(account.updatedAt)
        }
      })
    }
    console.log(`✅ Imported ${backupData.emailAccounts.length} email accounts`)

    console.log('💰 Importing payment catalog items...')
    for (const item of backupData.paymentItems) {
      await prisma.paymentCatalogItem.upsert({
        where: { id: item.id },
        update: {
          name: item.name,
          description: item.description,
          feeType: item.feeType,
          amount: item.amount,
          currency: item.currency,
          isActive: item.isActive,
          applicableFor: item.applicableFor,
          updatedAt: new Date()
        },
        create: {
          name: item.name,
          description: item.description,
          feeType: item.feeType,
          amount: item.amount,
          currency: item.currency,
          isActive: item.isActive,
          applicableFor: item.applicableFor,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        }
      })
    }
    console.log(`✅ Imported ${backupData.paymentItems.length} payment catalog items`)

    console.log('📊 Data import completed successfully!')
    
  } catch (error) {
    console.error('❌ Data import failed:', error)
    throw error
  }
}

async function createInitialData() {
  try {
    console.log('👤 Creating admin user...')
    const adminPassword = await bcrypt.hash('Navsaharan89@', 12)
    
    await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: adminPassword,
        updatedAt: new Date()
      },
      create: {
        email: '<EMAIL>',
        name: 'System Administrator',
        password: adminPassword,
        role: 'ADMIN'
      }
    })

    console.log('📧 Creating student email account...')
    const studentPassword = await bcrypt.hash('password123', 12)
    
    await prisma.emailAccount.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: studentPassword,
        updatedAt: new Date()
      },
      create: {
        email: '<EMAIL>',
        password: studentPassword,
        type: 'STUDENT',
        isActive: true,
        displayName: 'Student One',
        department: 'Computer Science',
        studentId: 'CS001'
      }
    })

    console.log('💰 Creating payment catalog items...')
    await prisma.paymentCatalogItem.upsert({
      where: { id: 1 },
      update: {
        name: 'Semester Fee',
        updatedAt: new Date()
      },
      create: {
        name: 'Semester Fee',
        description: 'Regular semester tuition fee',
        feeType: 'SEMESTER',
        amount: 15000,
        currency: 'INR',
        isActive: true,
        applicableFor: 'STUDENT'
      }
    })

    await prisma.paymentCatalogItem.upsert({
      where: { id: 2 },
      update: {
        name: 'Exam Fee',
        updatedAt: new Date()
      },
      create: {
        name: 'Exam Fee',
        description: 'End semester examination fee',
        feeType: 'EXAM',
        amount: 2000,
        currency: 'INR',
        isActive: true,
        applicableFor: 'STUDENT'
      }
    })

    console.log('✅ Initial data created successfully!')
    
  } catch (error) {
    console.error('❌ Initial data creation failed:', error)
    throw error
  }
}

// Run the setup
if (require.main === module) {
  setupCloudDatabase()
    .then(() => {
      console.log('\n🎯 Next steps:')
      console.log('1. Update your DATABASE_URL with your cloud database connection string')
      console.log('2. Run `npx prisma migrate deploy` in production')
      console.log('3. Test the application with the new database')
      console.log('4. Deploy to Vercel with the cloud database')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Setup failed:', error.message)
      process.exit(1)
    })
}

export { setupCloudDatabase }
