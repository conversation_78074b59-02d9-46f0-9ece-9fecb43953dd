import { prisma } from '../src/lib/prisma'

// Define the complete site structure based on the original website
const siteStructure = [
  {
    title: 'Home',
    slug: 'home',
    url: 'http://snpitc.in/Default.aspx',
    description: 'Welcome to SN Pvt ITI - Job oriented Industrial Training Courses',
    parentSlug: null,
    order: 1
  },
  {
    title: 'About Us',
    slug: 'about-us',
    url: null,
    description: 'Information about the institute',
    parentSlug: null,
    order: 2,
    children: [
      {
        title: 'About Institute',
        slug: 'about-institute',
        url: 'http://snpitc.in/aboutinstitute.aspx',
        description: 'Basic information and details about the institute',
        order: 1
      },
      {
        title: 'Introduction of Institute',
        slug: 'introduction-institute',
        url: 'http://snpitc.in/introductioninstitute.aspx',
        description: 'Detailed introduction, objectives, mission and vision',
        order: 2
      },
      {
        title: 'Scheme Running in The Institute',
        slug: 'scheme-running',
        url: 'http://snpitc.in/schemerunning.aspx',
        description: 'Current schemes and programs running in the institute',
        order: 3
      }
    ]
  },
  {
    title: 'Admissions',
    slug: 'admissions',
    url: null,
    description: 'Admission related information',
    parentSlug: null,
    order: 3,
    children: [
      {
        title: 'Admission Criteria',
        slug: 'admission-criteria',
        url: 'http://snpitc.in/admissioncriteria.aspx',
        description: 'Criteria and requirements for admission',
        order: 1
      },
      {
        title: 'Trades Affiliated To NCVT and SCVT',
        slug: 'trades-ncvt-scvt',
        url: 'http://snpitc.in/ncvtscvtaffilated.aspx',
        description: 'List of trades affiliated to NCVT and SCVT',
        order: 2
      },
      {
        title: 'Summary of Trades Affiliated To NCVT',
        slug: 'trades-ncvt',
        url: 'http://snpitc.in/ncvtaffilated.aspx',
        description: 'Summary of NCVT affiliated trades',
        order: 3
      },
      {
        title: 'Summary of Trades Affiliated To SCVT',
        slug: 'trades-scvt',
        url: 'http://snpitc.in/scvtaffilated.aspx',
        description: 'Summary of SCVT affiliated trades',
        order: 4
      },
      {
        title: 'Application Format',
        slug: 'application-format',
        url: 'http://snpitc.in/applicationformat.aspx',
        description: 'Application format and forms',
        order: 5
      },
      {
        title: 'Fee Structure',
        slug: 'fee-structure',
        url: 'http://snpitc.in/fee-structure.aspx',
        description: 'Fee structure for different courses',
        order: 6
      }
    ]
  },
  {
    title: 'Facilities',
    slug: 'facilities',
    url: null,
    description: 'Institute facilities and infrastructure',
    parentSlug: null,
    order: 4,
    children: [
      {
        title: 'Infrastructure, Building and Workshop',
        slug: 'infrastructure',
        url: 'http://snpitc.in/infrastructure.aspx',
        description: 'Infrastructure and building details',
        order: 1
      },
      {
        title: 'Trade Specific Infrastructure',
        slug: 'trade-infrastructure',
        url: 'http://snpitc.in/tsinfrastructure.aspx',
        description: 'Trade specific infrastructure details',
        order: 2
      },
      {
        title: 'Electric Power Supply',
        slug: 'electric-power',
        url: 'http://snpitc.in/electricpower.aspx',
        description: 'Electric power supply information',
        order: 3
      },
      {
        title: 'Library',
        slug: 'library',
        url: 'http://snpitc.in/library.aspx',
        description: 'Library facilities and resources',
        order: 4
      },
      {
        title: 'Computer Lab',
        slug: 'computer-lab',
        url: 'http://snpitc.in/computerlab.aspx',
        description: 'Computer lab facilities',
        order: 5
      },
      {
        title: 'Sports',
        slug: 'sports',
        url: 'http://snpitc.in/sports.aspx',
        description: 'Sports facilities and activities',
        order: 6
      }
    ]
  },
  {
    title: 'Contact',
    slug: 'contact',
    url: 'http://snpitc.in/contact.aspx',
    description: 'Contact information and location details',
    parentSlug: null,
    order: 10
  },
  {
    title: 'Gallery',
    slug: 'gallery',
    url: 'http://snpitc.in/gallery.aspx',
    description: 'Photo gallery of institute activities',
    parentSlug: null,
    order: 8
  },
  {
    title: 'Feedback',
    slug: 'feedback',
    url: 'http://snpitc.in/feedback.aspx',
    description: 'Feedback form for visitors',
    parentSlug: null,
    order: 9
  }
]

// Key content data extracted from the original site
const extractedContent = {
  siteInfo: {
    name: 'S.N. Pvt. Industrial Training Institute',
    tagline: 'Approved by Directorate of Technical Education, Govt. of Rajasthan',
    affiliation: 'Affiliated to NCVT (DGE&T) Govt. of India since 2009',
    address: 'D-117, Kaka Colony, Gandhi Vidhya Mandir, Teh.-Sardar Shahar, Dist. Churu',
    phone: '01564-275628',
    mobile: '**********',
    email: '<EMAIL>'
  },
  aboutInstitute: {
    establishmentDate: '17-08-2009',
    dgetFileRef: 'DGET-6/20/35/2009-TC(New Pvt)',
    dgetCode: 'P-574',
    location: 'Urban',
    contactNo: '**********'
  },
  introduction: {
    content: `SN Private Industrial Training Institute a constituent activity of Nav Chetana Shikhshan Sansthan was established in the year 2009 to fulfil the first of the objective of its managing society i.e. preparing employable youth through technical education.

Nav Chetana Shikhshan Sansthan the managing society of this institute is a society registered under Rajasthan societies registration act. 1958 in the office of Assistant Registrar (societies) vide registration no. 104/Churu/2008-2009 0n 15 Jan 2009.

This institute was founded by a retired police officer Shri RamiLal Saharan with an objective of overall development of the society with special emphasis on educating and imparting employable skills to the youth so that they can earn their livelihood and serve the nation.`,
    objectives: [
      'To introduce at least three most sought trades of the nearby industries viz. electrician, fitter, mechanic (diesel) in next 2-3 years.',
      'To expose students to minimum of three companies as industry inter phase program.',
      'To achieve 100 per cent placement for the passed out trainees.'
    ],
    vision: 'To be recognized as an Excellent Organization Providing World Class Technical Education at all Levels.',
    mission: 'To Strive for Excellence in Technical Education'
  }
}

// Function to populate database with extracted content
async function populateDatabase() {
  try {
    // Get admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!adminUser) {
      throw new Error('Admin user not found. Please run seed script first.')
    }

    console.log('Starting content population...')

    // Create pages from site structure
    for (const pageData of siteStructure) {
      // Create parent page
      const parentPage = await prisma.page.upsert({
        where: { slug: pageData.slug },
        update: {},
        create: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          status: 'PUBLISHED',
          order: pageData.order,
          createdById: adminUser.id,
          metaTitle: pageData.title,
          metaDesc: pageData.description
        }
      })

      console.log(`Created page: ${pageData.title}`)

      // Create child pages if they exist
      if (pageData.children) {
        for (const childData of pageData.children) {
          await prisma.page.upsert({
            where: { slug: childData.slug },
            update: {},
            create: {
              title: childData.title,
              slug: childData.slug,
              description: childData.description,
              status: 'PUBLISHED',
              order: childData.order,
              parentId: parentPage.id,
              createdById: adminUser.id,
              metaTitle: childData.title,
              metaDesc: childData.description
            }
          })

          console.log(`Created child page: ${childData.title}`)
        }
      }
    }

    // Add specific content for key pages
    const homePage = await prisma.page.findUnique({
      where: { slug: 'home' }
    })

    if (homePage) {
      // Add home page content blocks
      await prisma.content.create({
        data: {
          type: 'HTML',
          title: 'Welcome Message',
          content: `<h2>Welcome to SN Pvt ITI</h2>
          <p>S.N. Pvt. Industrial Training Institute welcomes you with Job oriented Industrial Training Courses. These courses help trainees for employment in public or private sector & Self employment...</p>`,
          order: 1,
          pageId: homePage.id,
          createdById: adminUser.id
        }
      })

      await prisma.content.create({
        data: {
          type: 'HTML',
          title: 'Introduction',
          content: `<h3>Introduction</h3>
          <p>${extractedContent.introduction.content}</p>`,
          order: 2,
          pageId: homePage.id,
          createdById: adminUser.id
        }
      })

      await prisma.content.create({
        data: {
          type: 'HTML',
          title: 'Vision & Mission',
          content: `<h3>Vision & Mission</h3>
          <h4>VISION:</h4>
          <p>${extractedContent.introduction.vision}</p>
          <h4>MISSION:</h4>
          <p>${extractedContent.introduction.mission}</p>`,
          order: 3,
          pageId: homePage.id,
          createdById: adminUser.id
        }
      })
    }

    console.log('Content population completed successfully!')

  } catch (error) {
    console.error('Error populating database:', error)
    throw error
  }
}

// Run the population if this script is executed directly
if (require.main === module) {
  populateDatabase()
    .then(() => {
      console.log('Database population completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Database population failed:', error)
      process.exit(1)
    })
}

export { siteStructure, extractedContent, populateDatabase }
