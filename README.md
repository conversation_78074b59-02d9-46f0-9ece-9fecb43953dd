# SNPITC Email & Payment Management System

A comprehensive email and payment management system built for educational institutions, featuring advanced admin controls, real-time monitoring, and multi-gateway payment processing.

## 🚀 Features

### Email Management System
- **Email Account Management**: Create and manage student/faculty email accounts
- **Real-time Monitoring**: Monitor email traffic, content, and security threats
- **Spam & Security Protection**: Advanced spam filtering and virus scanning
- **Compliance**: GDPR and FERPA compliant email handling
- **Audit Logging**: Comprehensive audit trails for all email activities

### Payment Management System
- **Multi-Gateway Support**: Razorpay, Stripe, PayU, Cashfree integration
- **Payment Catalog**: Manage fees, courses, and payment items
- **Transaction Monitoring**: Real-time payment tracking and analytics
- **Automated Billing**: Recurring payment support
- **Revenue Analytics**: Comprehensive financial reporting

### Admin Panel
- **Comprehensive Dashboard**: System overview and key metrics
- **User Management**: Role-based access control
- **System Monitoring**: Real-time health checks and alerts
- **Security Features**: Multi-factor authentication and audit logging
- **Responsive Design**: Mobile-friendly admin interface

### Security & Compliance
- **Data Encryption**: AES-256 encryption at rest and TLS 1.3 in transit
- **GDPR Compliance**: Data subject rights and privacy controls
- **FERPA Compliance**: Educational records protection
- **PCI DSS**: Secure payment processing
- **Audit Trails**: Comprehensive logging and monitoring

## 🛠️ Tech Stack

### Frontend
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern UI components
- **React Hook Form**: Form handling and validation

### Backend
- **Next.js API Routes**: Server-side API endpoints
- **Prisma ORM**: Type-safe database access
- **NextAuth.js**: Authentication and session management
- **bcryptjs**: Password hashing
- **Zod**: Runtime type validation

### Database & Storage
- **PostgreSQL**: Primary database
- **Redis**: Caching and session storage
- **Prisma**: Database ORM and migrations

### Payment Gateways
- **Razorpay**: Indian payment gateway
- **Stripe**: International payment processing
- **PayU**: Alternative payment gateway
- **Cashfree**: Additional payment options

### Monitoring & Logging
- **Prometheus**: Metrics collection
- **Grafana**: Monitoring dashboards
- **Loki**: Log aggregation
- **Promtail**: Log collection

### Infrastructure
- **Docker**: Containerization
- **Nginx**: Reverse proxy and load balancing
- **GitHub Actions**: CI/CD pipeline
- **Vercel**: Deployment platform (optional)

## 📋 Prerequisites

- **Node.js 18+**
- **PostgreSQL 15+**
- **Redis 7+**
- **Docker & Docker Compose**
- **Git**

## 🚀 Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/your-org/snpitc-remake.git
cd snpitc-remake
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.production.example .env.production

# Edit with your configuration
nano .env.production
```

### 3. Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Seed database
npx prisma db seed
```

### 4. Development Server

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### 5. Production Deployment

```bash
# Run automated deployment
chmod +x scripts/deploy-production.sh
./scripts/deploy-production.sh
```

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── admin/             # Admin panel pages
│   │   │   ├── email/         # Email management
│   │   │   └── payments/      # Payment management
│   │   └── api/               # API endpoints
│   │       ├── admin/         # Admin API routes
│   │       ├── auth/          # Authentication
│   │       └── webhooks/      # Payment webhooks
│   ├── components/            # React components
│   │   ├── admin/            # Admin-specific components
│   │   └── ui/               # Reusable UI components
│   ├── lib/                  # Utility functions
│   └── types/                # TypeScript definitions
├── prisma/                   # Database schema and migrations
├── docs/                     # Documentation
├── scripts/                  # Deployment and maintenance scripts
├── tests/                    # Test suites
├── monitoring/               # Monitoring configuration
└── nginx/                    # Nginx configuration
```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

```bash
# Application
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-super-secret-key
ENCRYPTION_KEY=your-encryption-key

# Database
DATABASE_URL=********************************/db

# Payment Gateways
RAZORPAY_KEY_ID=rzp_live_xxx
RAZORPAY_KEY_SECRET=xxx
STRIPE_SECRET_KEY=sk_live_xxx

# Email
SMTP_HOST=smtp.your-domain.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=xxx
```

See `.env.production.example` for complete configuration.

## 📚 Documentation

- **[Admin User Guide](docs/ADMIN_USER_GUIDE.md)**: Comprehensive admin panel usage
- **[API Documentation](docs/API_DOCUMENTATION.md)**: Complete API reference
- **[Security & Compliance](docs/SECURITY_COMPLIANCE.md)**: Security guidelines and compliance
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Production deployment instructions
- **[Testing Guide](docs/TESTING.md)**: Testing procedures and best practices
- **[Troubleshooting](docs/TROUBLESHOOTING_MAINTENANCE.md)**: Common issues and solutions
- **[Database Migration](docs/DATABASE_MIGRATION.md)**: Migration procedures

## 🧪 Testing

### Run All Tests

```bash
# Comprehensive test suite
npm run test:all
```

### Individual Test Types

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

## 🚀 Deployment

### Production Deployment

```bash
# Automated deployment
./scripts/deploy-production.sh
```

### Docker Deployment

```bash
# Build and start services
docker-compose -f docker-compose.production.yml up -d

# Health check
curl -f https://your-domain.com/api/health
```

### CI/CD Pipeline

The project includes GitHub Actions workflows for:
- Automated testing
- Security scanning
- Staging deployment
- Production deployment
- Rollback procedures

## 📊 Monitoring

### Access Monitoring Dashboards

- **Application**: `https://your-domain.com`
- **Grafana**: `https://your-domain.com:3001`
- **Prometheus**: `https://your-domain.com:9090`

### Key Metrics

- Email account statistics
- Payment transaction metrics
- System performance
- Security events
- User activity

## 🔒 Security

### Security Features

- **Multi-factor Authentication**: TOTP, SMS, Email
- **Role-based Access Control**: Granular permissions
- **Data Encryption**: AES-256 at rest, TLS 1.3 in transit
- **Audit Logging**: Comprehensive activity tracking
- **Rate Limiting**: API protection
- **Security Headers**: XSS, CSRF protection

### Compliance

- **GDPR**: Data subject rights, privacy controls
- **FERPA**: Educational records protection
- **PCI DSS**: Secure payment processing
- **SOC 2**: Security and availability controls

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Follow security guidelines
- Ensure GDPR/FERPA compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: Check the comprehensive docs in `/docs`
- **Issues**: Create GitHub issues for bugs and feature requests
- **Security**: Report security <NAME_EMAIL>
- **Support**: Contact <EMAIL>

### Emergency Contacts

- **System Administrator**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-call Support**: ******-SUPPORT

## 🎯 Roadmap

### Upcoming Features

- **Mobile App**: Native mobile applications
- **Advanced Analytics**: ML-powered insights
- **API Integrations**: Third-party service integrations
- **Multi-tenancy**: Support for multiple institutions
- **Advanced Reporting**: Custom report builder

### Version History

- **v1.0.0**: Initial release with email and payment management
- **v1.1.0**: Enhanced security and compliance features
- **v1.2.0**: Advanced monitoring and analytics
- **v2.0.0**: Multi-gateway payment support and mobile optimization

---

**Built with ❤️ for educational institutions**

For more information, visit our [documentation](docs/) or contact our support team.
