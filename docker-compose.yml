# SNPITC Email System - Docker Compose Configuration

version: '3.8'

services:
  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: snpitc-email-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://snpitc:${POSTGRES_PASSWORD}@postgres:5432/snpitc_email
      - NEXTAUTH_URL=https://${DOMAIN:-localhost:3000}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - REDIS_URL=redis://redis:6379
      - SMTP_HOST=localhost
      - SMTP_PORT=587
      - IMAP_HOST=localhost
      - IMAP_PORT=993
      - POP3_HOST=localhost
      - POP3_PORT=995
    volumes:
      - email_storage:/app/storage/emails
      - attachment_storage:/app/storage/attachments
      - app_logs:/app/storage/logs
    depends_on:
      - postgres
      - redis
    networks:
      - snpitc_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: snpitc-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=snpitc_email
      - POSTGRES_USER=snpitc
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - snpitc_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U snpitc -d snpitc_email"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: snpitc-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - snpitc_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: snpitc-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "587:587"   # SMTP
      - "993:993"   # IMAP SSL
      - "995:995"   # POP3 SSL
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - snpitc_network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: snpitc-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - snpitc_network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: snpitc-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - snpitc_network

  # Log Management with Loki
  loki:
    image: grafana/loki:latest
    container_name: snpitc-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - snpitc_network

  # Log Collection with Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: snpitc-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/log:/var/log/host:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - snpitc_network

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: snpitc-backup
    restart: "no"
    environment:
      - POSTGRES_DB=snpitc_email
      - POSTGRES_USER=snpitc
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - backup_data:/backup
      - ./scripts/backup.sh:/backup.sh:ro
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - postgres
    networks:
      - snpitc_network
    profiles:
      - backup

# Named Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  email_storage:
    driver: local
  attachment_storage:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  backup_data:
    driver: local

# Networks
networks:
  snpitc_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
