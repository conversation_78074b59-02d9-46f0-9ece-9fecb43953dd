import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function testAuthenticationFlow() {
  try {
    console.log('🔐 Testing authentication flow with Supabase...')
    
    // Test database connection
    await prisma.$connect()
    console.log('✅ Connected to Supabase database')
    
    // Test if we can perform basic operations
    console.log('🧪 Testing basic database operations...')
    
    // Try to query a simple table or create one if it doesn't exist
    try {
      // Test with a simple raw query first
      const result = await prisma.$queryRaw`SELECT 1 as test_connection`
      console.log('✅ Basic query successful:', result)
    } catch (error) {
      console.log('⚠️ Basic query failed:', error.message)
    }
    
    // Test environment variables
    console.log('🔧 Testing environment variables...')
    console.log('DATABASE_URL configured:', process.env.DATABASE_URL ? '✅ Yes' : '❌ No')
    console.log('SUPABASE_URL configured:', process.env.SUPABASE_URL ? '✅ Yes' : '❌ No')
    console.log('SUPABASE_ANON_KEY configured:', process.env.SUPABASE_ANON_KEY ? '✅ Yes' : '❌ No')
    console.log('NEXTAUTH_SECRET configured:', process.env.NEXTAUTH_SECRET ? '✅ Yes' : '❌ No')
    console.log('NEXTAUTH_URL configured:', process.env.NEXTAUTH_URL ? '✅ Yes' : '❌ No')
    
    // Test password hashing (for authentication)
    console.log('🔒 Testing password hashing...')
    const testPassword = 'Navsaharan89@'
    const hashedPassword = await bcrypt.hash(testPassword, 12)
    const isValid = await bcrypt.compare(testPassword, hashedPassword)
    console.log('Password hashing test:', isValid ? '✅ Working' : '❌ Failed')
    
    console.log('🎉 Authentication flow test completed!')
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message)
    
    if (error.code === 'P1001') {
      console.log('\n💡 Database connection troubleshooting:')
      console.log('1. Verify DATABASE_URL is correct')
      console.log('2. Check Supabase project status')
      console.log('3. Ensure database is accessible')
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

testAuthenticationFlow()
  .then(() => {
    console.log('\n🎯 Next steps for complete setup:')
    console.log('1. Run the SQL migration script in Supabase SQL Editor')
    console.log('2. Execute the data migration script')
    console.log('3. Start the development server')
    console.log('4. Test login functionality')
    console.log('')
    console.log('📁 Files to use:')
    console.log('   - scripts/supabase-migration.sql (run in Supabase)')
    console.log('   - scripts/migrate-data-to-supabase.ts (run locally)')
    console.log('')
    console.log('🔑 Test credentials after setup:')
    console.log('   Admin: <EMAIL> / Navsaharan89@')
    console.log('   Student: <EMAIL> / password123')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error.message)
    process.exit(1)
  })
