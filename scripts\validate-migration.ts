#!/usr/bin/env tsx

/**
 * Migration Validation Script
 * 
 * Validates that the database migration was successful and all
 * email and payment integration features are working correctly.
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

interface ValidationResult {
  test: string
  passed: boolean
  message: string
  details?: any
}

class MigrationValidator {
  private results: ValidationResult[] = []

  async runValidation(): Promise<boolean> {
    console.log('🔍 Starting migration validation...\n')

    const tests = [
      this.validateSchemaExists,
      this.validateRelations,
      this.validateIndexes,
      this.validateSampleData,
      this.validateAPICompatibility,
      this.validateSecurity
    ]

    for (const test of tests) {
      try {
        await test.call(this)
      } catch (error) {
        this.results.push({
          test: test.name,
          passed: false,
          message: `Test failed with error: ${error}`,
          details: error
        })
      }
    }

    this.printResults()
    return this.results.every(r => r.passed)
  }

  private async validateSchemaExists() {
    console.log('📋 Validating schema exists...')

    // Check if all required tables exist
    const requiredTables = [
      'users',
      'email_accounts',
      'emails',
      'payments',
      'payment_catalog_items',
      'admin_audit_logs',
      'system_alerts'
    ]

    for (const table of requiredTables) {
      try {
        await prisma.$queryRawUnsafe(`SELECT 1 FROM ${table} LIMIT 1`)
        this.results.push({
          test: `Table ${table} exists`,
          passed: true,
          message: `Table ${table} is accessible`
        })
      } catch (error) {
        this.results.push({
          test: `Table ${table} exists`,
          passed: false,
          message: `Table ${table} is not accessible`,
          details: error
        })
      }
    }

    // Check if new enums exist
    try {
      await prisma.$queryRaw`SELECT unnest(enum_range(NULL::RecurringInterval))`
      this.results.push({
        test: 'RecurringInterval enum exists',
        passed: true,
        message: 'RecurringInterval enum is accessible'
      })
    } catch (error) {
      this.results.push({
        test: 'RecurringInterval enum exists',
        passed: false,
        message: 'RecurringInterval enum is not accessible',
        details: error
      })
    }
  }

  private async validateRelations() {
    console.log('🔗 Validating relations...')

    try {
      // Test User -> PaymentCatalogItem relation
      const userWithPaymentItems = await prisma.user.findFirst({
        include: {
          createdPaymentItems: true,
          adminActions: true
        }
      })

      this.results.push({
        test: 'User relations work',
        passed: true,
        message: 'User relations are working correctly',
        details: {
          paymentItems: userWithPaymentItems?.createdPaymentItems?.length || 0,
          adminActions: userWithPaymentItems?.adminActions?.length || 0
        }
      })

      // Test Payment -> PaymentCatalogItem relation
      const paymentWithCatalogItem = await prisma.payment.findFirst({
        include: {
          catalogItem: true,
          student: true
        }
      })

      this.results.push({
        test: 'Payment relations work',
        passed: true,
        message: 'Payment relations are working correctly',
        details: {
          hasCatalogItem: !!paymentWithCatalogItem?.catalogItem,
          hasStudent: !!paymentWithCatalogItem?.student
        }
      })

    } catch (error) {
      this.results.push({
        test: 'Relations validation',
        passed: false,
        message: 'Relations validation failed',
        details: error
      })
    }
  }

  private async validateIndexes() {
    console.log('📊 Validating indexes...')

    const expectedIndexes = [
      'idx_emails_created_at',
      'idx_emails_from_email',
      'idx_payments_created_at',
      'idx_payments_status',
      'idx_audit_logs_created_at'
    ]

    for (const indexName of expectedIndexes) {
      try {
        // Check if index exists (PostgreSQL specific)
        const result = await prisma.$queryRawUnsafe(`
          SELECT indexname FROM pg_indexes 
          WHERE indexname = '${indexName}'
        `) as any[]

        this.results.push({
          test: `Index ${indexName} exists`,
          passed: result.length > 0,
          message: result.length > 0 
            ? `Index ${indexName} exists` 
            : `Index ${indexName} not found`
        })
      } catch (error) {
        this.results.push({
          test: `Index ${indexName} validation`,
          passed: false,
          message: `Could not validate index ${indexName}`,
          details: error
        })
      }
    }
  }

  private async validateSampleData() {
    console.log('📝 Validating sample data...')

    try {
      // Check admin user exists
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      })

      this.results.push({
        test: 'Admin user exists',
        passed: !!adminUser,
        message: adminUser ? 'Admin user found' : 'Admin user not found'
      })

      // Check payment catalog items exist
      const catalogItemCount = await prisma.paymentCatalogItem.count()

      this.results.push({
        test: 'Payment catalog items exist',
        passed: catalogItemCount > 0,
        message: `Found ${catalogItemCount} payment catalog items`
      })

      // Check email accounts exist
      const emailAccountCount = await prisma.emailAccount.count()

      this.results.push({
        test: 'Email accounts exist',
        passed: emailAccountCount > 0,
        message: `Found ${emailAccountCount} email accounts`
      })

    } catch (error) {
      this.results.push({
        test: 'Sample data validation',
        passed: false,
        message: 'Sample data validation failed',
        details: error
      })
    }
  }

  private async validateAPICompatibility() {
    console.log('🔌 Validating API compatibility...')

    try {
      // Test complex queries that the API will use
      
      // Email account management query
      const emailAccountsWithStats = await prisma.emailAccount.findMany({
        take: 5,
        include: {
          _count: {
            select: {
              sentEmails: true,
              receivedEmails: true,
              payments: true
            }
          }
        }
      })

      this.results.push({
        test: 'Email account API queries work',
        passed: true,
        message: `Successfully queried ${emailAccountsWithStats.length} email accounts with stats`
      })

      // Payment catalog query
      const catalogItemsWithStats = await prisma.paymentCatalogItem.findMany({
        take: 5,
        include: {
          _count: {
            select: {
              payments: true
            }
          },
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      this.results.push({
        test: 'Payment catalog API queries work',
        passed: true,
        message: `Successfully queried ${catalogItemsWithStats.length} catalog items with stats`
      })

      // Payment transaction query
      const paymentsWithDetails = await prisma.payment.findMany({
        take: 5,
        include: {
          student: {
            select: {
              email: true,
              displayName: true,
              type: true
            }
          },
          catalogItem: {
            select: {
              name: true,
              category: true
            }
          }
        }
      })

      this.results.push({
        test: 'Payment transaction API queries work',
        passed: true,
        message: `Successfully queried ${paymentsWithDetails.length} payments with details`
      })

    } catch (error) {
      this.results.push({
        test: 'API compatibility validation',
        passed: false,
        message: 'API compatibility validation failed',
        details: error
      })
    }
  }

  private async validateSecurity() {
    console.log('🔒 Validating security features...')

    try {
      // Check audit logging works
      const auditLogCount = await prisma.adminAuditLog.count()

      this.results.push({
        test: 'Audit logging works',
        passed: auditLogCount > 0,
        message: `Found ${auditLogCount} audit log entries`
      })

      // Check system alerts work
      const alertCount = await prisma.systemAlert.count()

      this.results.push({
        test: 'System alerts work',
        passed: alertCount >= 0, // 0 is acceptable for alerts
        message: `Found ${alertCount} system alerts`
      })

      // Validate password hashing (check if passwords are hashed)
      const emailAccount = await prisma.emailAccount.findFirst({
        select: { password: true }
      })

      const isPasswordHashed = emailAccount?.password && 
        emailAccount.password.length > 20 && 
        emailAccount.password.startsWith('$')

      this.results.push({
        test: 'Password hashing works',
        passed: !!isPasswordHashed,
        message: isPasswordHashed ? 'Passwords are properly hashed' : 'Password hashing may not be working'
      })

    } catch (error) {
      this.results.push({
        test: 'Security validation',
        passed: false,
        message: 'Security validation failed',
        details: error
      })
    }
  }

  private printResults() {
    console.log('\n📊 Validation Results:')
    console.log('=' .repeat(60))

    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length

    for (const result of this.results) {
      const status = result.passed ? '✅' : '❌'
      console.log(`${status} ${result.test}: ${result.message}`)
      
      if (!result.passed && result.details) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`)
      }
    }

    console.log('=' .repeat(60))
    console.log(`📈 Summary: ${passed}/${total} tests passed`)
    
    if (passed === total) {
      console.log('🎉 All validation tests passed! Migration is successful.')
    } else {
      console.log('⚠️  Some validation tests failed. Please review the issues above.')
    }
  }
}

// Main execution
async function main() {
  const validator = new MigrationValidator()
  
  try {
    const success = await validator.runValidation()
    process.exit(success ? 0 : 1)
  } catch (error) {
    console.error('❌ Validation script failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  main()
}

export { MigrationValidator }
