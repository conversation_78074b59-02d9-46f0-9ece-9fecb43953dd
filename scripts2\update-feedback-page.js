const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateFeedbackPage() {
  try {
    console.log('Updating feedback page with functional form...')

    // Find the feedback page
    const feedbackPage = await prisma.page.findUnique({
      where: { slug: 'feedback' }
    })

    if (!feedbackPage) {
      console.log('Feedback page not found')
      return
    }

    // Get the first admin user for createdById
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!adminUser) {
      console.log('No admin user found')
      return
    }

    // Update the page content to include the functional form
    const updatedContent = `
      <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
        <h2 class="text-2xl font-bold mb-6">Feedback</h2>
        <p class="mb-6">We value your feedback and suggestions. Please use the form below to send us your enquiries and grievances.</p>
      </div>
    `

    await prisma.page.update({
      where: { id: feedbackPage.id },
      data: { content: updatedContent }
    })

    // Delete existing content blocks for feedback page
    await prisma.content.deleteMany({
      where: { pageId: feedbackPage.id }
    })

    // Add the functional feedback form as a content block
    await prisma.content.create({
      data: {
        pageId: feedbackPage.id,
        type: 'FEEDBACK_FORM',
        title: 'Send Enquiry & Grievances',
        content: '',
        order: 1,
        createdById: adminUser.id
      }
    })

    // Add contact information section
    await prisma.content.create({
      data: {
        pageId: feedbackPage.id,
        type: 'HTML',
        title: 'Additional Information',
        content: `
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            <div class="bg-green-50 p-6 rounded-lg">
              <h3 class="text-lg font-semibold text-green-900 mb-4">Contact Information</h3>
              <ul class="text-green-800 space-y-2">
                <li>• Phone: 01564-275628</li>
                <li>• Mobile: 9414947801</li>
                <li>• Email: <EMAIL></li>
                <li>• Address: D-117, Kaka Colony, Gandhi Vidhya Mandir</li>
                <li>• Teh.-Sardar Shahar, Dist. Churu</li>
              </ul>
            </div>
            <div class="bg-yellow-50 p-6 rounded-lg">
              <h3 class="text-lg font-semibold text-yellow-900 mb-4">Feedback Categories</h3>
              <ul class="text-yellow-800 space-y-2">
                <li>• Academic queries</li>
                <li>• Admission information</li>
                <li>• Infrastructure feedback</li>
                <li>• Placement assistance</li>
                <li>• General suggestions</li>
              </ul>
            </div>
          </div>
        `,
        order: 2,
        createdById: adminUser.id
      }
    })

    console.log('✅ Feedback page updated successfully with functional form')

  } catch (error) {
    console.error('❌ Error updating feedback page:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateFeedbackPage()
